/**
 * CloudFlare 验证组件工具函数
 * 基于 cocos-ca-h5 项目重构优化
 */

import type { ErrorInfo, StorageKeys, PlatformInfo } from "./types";

/**
 * 本地存储管理工具
 */
export class LocalStorage {
  /**
   * 设置本地存储项
   * @param key 键
   * @param value 值
   */
  static setItem<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(key, serializedValue);
    } catch (error) {
      console.error("LocalStorage setItem error:", error);
    }
  }

  /**
   * 获取本地存储项
   * @param key 键
   * @param defaultValue 默认值
   * @returns 存储的值或默认值
   */
  static getItem<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      if (item === null) {
        return defaultValue;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error("LocalStorage getItem error:", error);
      return defaultValue;
    }
  }

  /**
   * 移除本地存储项
   * @param key 键
   */
  static removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error("LocalStorage removeItem error:", error);
    }
  }

  /**
   * 清空本地存储
   */
  static clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error("LocalStorage clear error:", error);
    }
  }

  /**
   * 检查键是否存在
   * @param key 键
   * @returns 是否存在
   */
  static hasItem(key: string): boolean {
    try {
      return localStorage.getItem(key) !== null;
    } catch (error) {
      console.error("LocalStorage hasItem error:", error);
      return false;
    }
  }
}

/**
 * 错误处理工具
 */
export class ErrorHandler {
  private static errors: ErrorInfo[] = [];

  /**
   * 记录错误
   * @param error 错误对象
   * @param context 错误上下文
   */
  static logError(error: any, context?: string): void {
    const errorInfo: ErrorInfo = {
      message: error?.message || String(error),
      code: error?.code,
      stack: error?.stack,
      timestamp: Date.now(),
    };

    this.errors.push(errorInfo);

    // 控制台输出
    console.error(`[${context || "Unknown"}] Error:`, errorInfo);

    // 限制错误记录数量
    if (this.errors.length > 100) {
      this.errors = this.errors.slice(-50);
    }
  }

  /**
   * 获取错误历史
   * @returns 错误列表
   */
  static getErrors(): ErrorInfo[] {
    return [...this.errors];
  }

  /**
   * 清空错误历史
   */
  static clearErrors(): void {
    this.errors = [];
  }

  /**
   * 获取最近的错误
   * @returns 最近的错误或 null
   */
  static getLastError(): ErrorInfo | null {
    return this.errors.length > 0 ? this.errors[this.errors.length - 1] : null;
  }
}

/**
 * 手机号验证工具
 */
export class PhoneValidator {
  /**
   * 验证菲律宾手机号
   * @param phone 手机号
   * @returns 是否有效
   */
  static isValidPhilippinePhone(phone: string): boolean {
    if (!phone) return false;

    // 移除空格和特殊字符
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, "");

    // 菲律宾手机号格式：09xxxxxxxxx 或 +639xxxxxxxxx
    const phoneRegex = /^(09|\+639)\d{9}$/;
    return phoneRegex.test(cleanPhone);
  }

  /**
   * 格式化手机号显示（隐藏中间部分）
   * @param phone 手机号
   * @returns 格式化后的手机号
   */
  static formatPhoneDisplay(phone: string): string {
    if (!phone || phone.length < 6) return phone;

    const cleanPhone = phone.replace(/[\s\-\(\)]/g, "");
    if (cleanPhone.length <= 6) return cleanPhone;

    return `${cleanPhone.slice(0, 2)}****${cleanPhone.slice(-4)}`;
  }

  /**
   * 标准化手机号格式
   * @param phone 手机号
   * @returns 标准化后的手机号
   */
  static normalizePhone(phone: string): string {
    if (!phone) return "";

    // 移除空格和特殊字符
    let cleanPhone = phone.replace(/[\s\-\(\)]/g, "");

    // 如果以 +63 开头，转换为 09 格式
    if (cleanPhone.startsWith("+639")) {
      cleanPhone = "09" + cleanPhone.slice(4);
    }

    return cleanPhone;
  }
}

/**
 * 倒计时工具
 */
export class CountdownManager {
  private static timers: Map<string, NodeJS.Timeout> = new Map();

  /**
   * 开始倒计时
   * @param key 倒计时标识
   * @param duration 持续时间（秒）
   * @param onTick 每秒回调
   * @param onComplete 完成回调
   */
  static start(
    key: string,
    duration: number,
    onTick: (remaining: number) => void,
    onComplete: () => void
  ): void {
    // 清除已存在的计时器
    this.stop(key);

    const endTime = Date.now() + duration * 1000;
    LocalStorage.setItem(`countdown_${key}`, endTime);

    const timer = setInterval(() => {
      const remaining = Math.max(0, endTime - Date.now());
      const seconds = Math.ceil(remaining / 1000);

      onTick(seconds);

      if (remaining <= 0) {
        this.stop(key);
        onComplete();
      }
    }, 1000);

    this.timers.set(key, timer);

    // 立即执行一次
    const remaining = Math.max(0, endTime - Date.now());
    const seconds = Math.ceil(remaining / 1000);
    onTick(seconds);
  }

  /**
   * 停止倒计时
   * @param key 倒计时标识
   */
  static stop(key: string): void {
    const timer = this.timers.get(key);
    if (timer) {
      clearInterval(timer);
      this.timers.delete(key);
    }
    LocalStorage.removeItem(`countdown_${key}`);
  }

  /**
   * 检查是否有活跃的倒计时
   * @param key 倒计时标识
   * @returns 剩余时间（秒）或 0
   */
  static getRemaining(key: string): number {
    const endTime = LocalStorage.getItem(`countdown_${key}`, 0);
    if (!endTime) return 0;

    const remaining = Math.max(0, endTime - Date.now());
    return Math.ceil(remaining / 1000);
  }

  /**
   * 恢复倒计时（页面刷新后）
   * @param key 倒计时标识
   * @param onTick 每秒回调
   * @param onComplete 完成回调
   * @returns 是否成功恢复
   */
  static restore(
    key: string,
    onTick: (remaining: number) => void,
    onComplete: () => void
  ): boolean {
    const remaining = this.getRemaining(key);
    if (remaining <= 0) return false;

    this.start(key, remaining, onTick, onComplete);
    return true;
  }

  /**
   * 清理所有倒计时
   */
  static clearAll(): void {
    this.timers.forEach((timer, key) => {
      this.stop(key);
    });
  }
}

/**
 * URL 工具（基于 cocos-ca-h5 的实现）
 */
export class UrlUtils {
  /**
   * 构建 CloudFlare 验证 URL
   * @param baseUrl 基础 URL
   * @param siteKey 站点密钥
   * @param appearance 外观模式
   * @param options 其他选项
   * @returns 完整的验证 URL
   */
  static buildVerifyUrl(
    baseUrl: string,
    siteKey: string,
    appearance: string = "always",
    options: Record<string, any> = {}
  ): string {
    const params = new URLSearchParams({
      siteKey: encodeURIComponent(siteKey),
      appearance,
      isNative: "0",
      os: this.getOS(),
      ...options,
    });

    return `${baseUrl}turnstile.html?${params.toString()}`;
  }

  /**
   * 手动解析URL参数（基于 cocos-ca-h5 的实现）
   * @param url URL字符串
   * @returns 包含参数的普通对象
   */
  static parseUrlParams(url: string): Record<string, string> {
    const params: Record<string, string> = {};

    try {
      // 查找问号位置
      const questionMarkIndex = url.indexOf("?");
      if (questionMarkIndex === -1) {
        return params;
      }

      // 提取查询字符串
      const queryString = url.substring(questionMarkIndex + 1);

      // 分割参数
      const pairs = queryString.split("&");
      for (const pair of pairs) {
        const equalIndex = pair.indexOf("=");
        if (equalIndex !== -1) {
          const key = pair.substring(0, equalIndex);
          const value = pair.substring(equalIndex + 1);
          if (key && value) {
            try {
              params[decodeURIComponent(key)] = decodeURIComponent(value);
            } catch (e) {
              // 如果解码失败，使用原始值
              params[key] = value;
            }
          }
        }
      }
    } catch (error) {
      console.error("UrlUtils parseUrlParams error: ", error);
    }

    return params;
  }

  /**
   * 创建 Blob URL 避免跨域问题
   * @param htmlText HTML 文本
   * @returns Blob URL
   */
  static createHtmlBlobUrl(htmlText: string): string {
    try {
      const blob = new Blob([htmlText], { type: "text/html" });
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error("UrlUtils createHtmlBlobUrl error: ", error);
      return "";
    }
  }

  /**
   * 获取操作系统类型
   * @returns 操作系统标识
   */
  private static getOS(): string {
    const platform = navigator.platform.toLowerCase();
    const userAgent = navigator.userAgent.toLowerCase();

    if (platform.includes("win")) return "windows";
    if (platform.includes("mac")) return "macos";
    if (userAgent.includes("android")) return "android";
    if (userAgent.includes("iphone") || userAgent.includes("ipad")) return "ios";
    return "linux";
  }
}

/**
 * 设备检测工具
 */
export class DeviceUtils {
  /**
   * 检测是否为移动设备
   * @returns 是否为移动设备
   */
  static isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  /**
   * 检测是否需要屏幕上移（移动设备键盘弹出时）
   * @returns 是否需要上移
   */
  static needScreenUp(): boolean {
    return this.isMobile() && window.innerHeight < 600;
  }

  /**
   * 获取视口高度
   * @returns 视口高度
   */
  static getViewportHeight(): number {
    return window.innerHeight || document.documentElement.clientHeight;
  }

  /**
   * 获取视口宽度
   * @returns 视口宽度
   */
  static getViewportWidth(): number {
    return window.innerWidth || document.documentElement.clientWidth;
  }
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * 时间锁工具类（基于 cocos-ca-h5 的 TimeLock 实现）
 */
export class TimeLock {
  private _lock: boolean = false;
  private _timeout: NodeJS.Timeout | null = null;
  private _duration: number;

  constructor(duration: number = 3000) {
    this._duration = duration;
  }

  get lock(): boolean {
    return this._lock;
  }

  set lock(value: boolean) {
    this._lock = value;

    if (this._timeout) {
      clearTimeout(this._timeout);
      this._timeout = null;
    }

    if (value) {
      this._timeout = setTimeout(() => {
        this._lock = false;
        this._timeout = null;
      }, this._duration);
    }
  }

  /**
   * 设置锁定时长
   * @param duration 时长（毫秒）
   */
  setDuration(duration: number): void {
    this._duration = duration;
  }

  /**
   * 清除锁定
   */
  clear(): void {
    this._lock = false;
    if (this._timeout) {
      clearTimeout(this._timeout);
      this._timeout = null;
    }
  }
}

/**
 * 平台检测工具类（基于 cocos-ca-h5 的实现）
 */
export class PlatformUtils {
  /**
   * 获取平台信息
   * @returns 平台信息对象
   */
  static getPlatformInfo(): PlatformInfo {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform.toLowerCase();

    let os: PlatformInfo["os"] = "web";

    if (platform.includes("win")) {
      os = "windows";
    } else if (platform.includes("mac")) {
      os = "macos";
    } else if (userAgent.includes("android")) {
      os = "android";
    } else if (userAgent.includes("iphone") || userAgent.includes("ipad")) {
      os = "ios";
    } else if (platform.includes("linux")) {
      os = "linux";
    }

    return {
      isNative: false, // Web 环境下始终为 false
      os,
      userAgent: navigator.userAgent,
    };
  }

  /**
   * 检查是否为移动设备
   * @returns 是否为移动设备
   */
  static isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  /**
   * 检查是否为 Android 设备
   * @returns 是否为 Android
   */
  static isAndroid(): boolean {
    return /Android/i.test(navigator.userAgent);
  }

  /**
   * 检查是否为 iOS 设备
   * @returns 是否为 iOS
   */
  static isIOS(): boolean {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent);
  }

  /**
   * 获取设备像素比
   * @returns 设备像素比
   */
  static getDevicePixelRatio(): number {
    return window.devicePixelRatio || 1;
  }
}

/**
 * 性能监控工具类
 */
export class PerformanceUtils {
  private static marks: Map<string, number> = new Map();

  /**
   * 开始性能标记
   * @param name 标记名称
   */
  static mark(name: string): void {
    this.marks.set(name, performance.now());
  }

  /**
   * 测量性能
   * @param name 标记名称
   * @returns 耗时（毫秒）
   */
  static measure(name: string): number {
    const startTime = this.marks.get(name);
    if (startTime === undefined) {
      console.warn(`Performance mark "${name}" not found`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.marks.delete(name);
    return duration;
  }

  /**
   * 获取内存使用情况
   * @returns 内存信息
   */
  static getMemoryInfo(): any {
    // @ts-ignore
    return (performance as any).memory || null;
  }
}

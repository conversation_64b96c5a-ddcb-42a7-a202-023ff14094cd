{"name": "vue3-phone-verification", "version": "1.0.0", "description": "Vue3 phone verification components converted from Cocos Creator TypeScript", "main": "index.js", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "typescript": "~5.2.0", "vite": "^4.4.0", "vue-tsc": "^1.8.0"}, "keywords": ["vue3", "phone-verification", "cloudflare", "sms", "typescript", "cocos-creator"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/vue3-phone-verification.git"}, "bugs": {"url": "https://github.com/your-username/vue3-phone-verification/issues"}, "homepage": "https://github.com/your-username/vue3-phone-verification#readme"}
<template>
  <div class="cloudflare-test">
    <div class="header">
      <h1>🔐 Cloudflare 验证测试</h1>
      <p>测试 Cloudflare Turnstile 验证功能</p>
      <button @click="goBack" class="back-btn">← 返回测试首页</button>
    </div>

    <div class="test-container">
      <!-- 基础验证测试 -->
      <div class="test-section">
        <h2>🎯 基础验证测试</h2>
        <div class="test-grid">
          <div class="test-card">
            <h3>登录验证</h3>
            <p>测试登录场景的 Cloudflare 验证</p>
            <button 
              @click="testLoginVerify" 
              :disabled="isLoading"
              class="test-btn primary"
            >
              {{ isLoading ? '验证中...' : '测试登录验证' }}
            </button>
          </div>

          <div class="test-card">
            <h3>获取验证码</h3>
            <p>测试获取验证码场景的验证</p>
            <button 
              @click="testGetCodeVerify" 
              :disabled="isLoading"
              class="test-btn secondary"
            >
              {{ isLoading ? '验证中...' : '测试获取验证码' }}
            </button>
          </div>

          <div class="test-card">
            <h3>忘记密码</h3>
            <p>测试忘记密码场景的验证</p>
            <button 
              @click="testForgetPasswordVerify" 
              :disabled="isLoading"
              class="test-btn warning"
            >
              {{ isLoading ? '验证中...' : '测试忘记密码' }}
            </button>
          </div>

          <div class="test-card">
            <h3>提现验证</h3>
            <p>测试提现场景的验证</p>
            <button 
              @click="testWithdrawalVerify" 
              :disabled="isLoading"
              class="test-btn danger"
            >
              {{ isLoading ? '验证中...' : '测试提现验证' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 高级验证测试 -->
      <div class="test-section">
        <h2>⚙️ 高级验证测试</h2>
        <div class="test-grid">
          <div class="test-card">
            <h3>KYC 验证</h3>
            <p>测试 KYC 信息提交验证</p>
            <button 
              @click="testKycVerify" 
              :disabled="isLoading"
              class="test-btn info"
            >
              {{ isLoading ? '验证中...' : '测试 KYC 验证' }}
            </button>
          </div>

          <div class="test-card">
            <h3>绑定手机号</h3>
            <p>测试绑定手机号验证</p>
            <button 
              @click="testBindPhoneVerify" 
              :disabled="isLoading"
              class="test-btn success"
            >
              {{ isLoading ? '验证中...' : '测试绑定手机' }}
            </button>
          </div>

          <div class="test-card">
            <h3>修改支付密码</h3>
            <p>测试修改支付密码验证</p>
            <button 
              @click="testModifyPayPasswordVerify" 
              :disabled="isLoading"
              class="test-btn purple"
            >
              {{ isLoading ? '验证中...' : '测试修改支付密码' }}
            </button>
          </div>

          <div class="test-card">
            <h3>Token 管理</h3>
            <p>测试 Token 消费功能</p>
            <button 
              @click="testTokenConsume" 
              :disabled="!hasToken"
              class="test-btn dark"
            >
              {{ hasToken ? '消费 Token' : '无可用 Token' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 验证结果显示 -->
      <div class="test-section" v-if="lastResult">
        <h2>📊 验证结果</h2>
        <div class="result-card">
          <div class="result-header">
            <h3>最后验证结果</h3>
            <span :class="['status', lastResult.success ? 'success' : 'failed']">
              {{ lastResult.success ? '✅ 成功' : '❌ 失败' }}
            </span>
          </div>
          <div class="result-details">
            <div class="detail-item">
              <label>验证场景:</label>
              <span>{{ lastResult.scene }}</span>
            </div>
            <div class="detail-item">
              <label>返回代码:</label>
              <span>{{ lastResult.code }}</span>
            </div>
            <div class="detail-item">
              <label>Token 状态:</label>
              <span>{{ lastResult.token ? '已获取' : '未获取' }}</span>
            </div>
            <div class="detail-item">
              <label>验证时间:</label>
              <span>{{ lastResult.timestamp }}</span>
            </div>
          </div>
          <div class="result-actions">
            <button @click="clearResult" class="clear-btn">清除结果</button>
            <button @click="copyResult" class="copy-btn">复制结果</button>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="test-section">
        <h2>📖 使用说明</h2>
        <div class="info-card">
          <h3>测试步骤</h3>
          <ol>
            <li>选择要测试的验证场景</li>
            <li>点击对应的测试按钮</li>
            <li>在弹出的验证窗口中完成验证</li>
            <li>查看验证结果和返回的 Token</li>
            <li>可以使用 "消费 Token" 按钮测试 Token 管理</li>
          </ol>
          
          <h3>验证场景说明</h3>
          <ul>
            <li><strong>登录验证:</strong> 用户登录时的安全验证</li>
            <li><strong>获取验证码:</strong> 手机验证码获取前的验证</li>
            <li><strong>忘记密码:</strong> 密码重置流程的验证</li>
            <li><strong>提现验证:</strong> 资金提取时的安全验证</li>
            <li><strong>KYC 验证:</strong> 身份认证信息提交验证</li>
            <li><strong>绑定手机:</strong> 绑定新手机号的验证</li>
            <li><strong>修改支付密码:</strong> 支付密码变更验证</li>
          </ul>

          <h3>注意事项</h3>
          <ul>
            <li>确保网络连接正常</li>
            <li>验证窗口可能需要几秒钟加载</li>
            <li>每次验证后 Token 会被自动消费</li>
            <li>如果验证失败，请检查配置是否正确</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";
import { useCloudflareVerify, CloudFlareScene } from "@/composables/useCloudflareVerify";

const router = useRouter();
const { isLoading, getVerifyToken, consumeToken } = useCloudflareVerify();

// 响应式数据
const lastResult = ref<{
  scene: string;
  success: boolean;
  token: string;
  code: number;
  timestamp: string;
} | null>(null);

// 计算属性
const hasToken = computed(() => {
  return lastResult.value?.token && lastResult.value.success;
});

// 返回测试首页
const goBack = () => {
  router.push("/test");
};

// 通用验证处理函数
const handleVerification = async (scene: CloudFlareScene, sceneName: string) => {
  try {
    const result = await getVerifyToken(scene);
    
    lastResult.value = {
      scene: sceneName,
      success: !!result.token,
      token: result.token,
      code: result.code,
      timestamp: new Date().toLocaleString()
    };
    
    if (result.token) {
      showToast(`${sceneName}验证成功!`);
    } else {
      showToast(`${sceneName}验证失败!`);
    }
  } catch (error) {
    console.error(`${sceneName} verification error:`, error);
    showToast("验证过程中发生错误");
    
    lastResult.value = {
      scene: sceneName,
      success: false,
      token: "",
      code: -1,
      timestamp: new Date().toLocaleString()
    };
  }
};

// 各种验证测试方法
const testLoginVerify = () => handleVerification(CloudFlareScene.LOGIN_SUBMIT, "登录");
const testGetCodeVerify = () => handleVerification(CloudFlareScene.LOGIN_PHONE_GET_CODE, "获取验证码");
const testForgetPasswordVerify = () => handleVerification(CloudFlareScene.FORGET_PW_SUBMIT, "忘记密码");
const testWithdrawalVerify = () => handleVerification(CloudFlareScene.WITHDRAWAL_SUBMIT, "提现");
const testKycVerify = () => handleVerification(CloudFlareScene.KYC_SUBMIT, "KYC");
const testBindPhoneVerify = () => handleVerification(CloudFlareScene.BIND_PHONE_SUBMIT, "绑定手机号");
const testModifyPayPasswordVerify = () => handleVerification(CloudFlareScene.MODIFY_PAY_PW_SUBMIT, "修改支付密码");

// Token 消费测试
const testTokenConsume = () => {
  const tokenData = consumeToken();
  if (tokenData) {
    showToast("Token 消费成功!");
    console.log("Consumed token:", tokenData);
    lastResult.value = null;
  } else {
    showToast("没有可消费的 Token");
  }
};

// 清除结果
const clearResult = () => {
  lastResult.value = null;
  showToast("结果已清除");
};

// 复制结果
const copyResult = async () => {
  if (!lastResult.value) return;
  
  const resultText = `
Cloudflare 验证结果:
场景: ${lastResult.value.scene}
状态: ${lastResult.value.success ? '成功' : '失败'}
代码: ${lastResult.value.code}
Token: ${lastResult.value.token ? '已获取' : '未获取'}
时间: ${lastResult.value.timestamp}
  `.trim();
  
  try {
    await navigator.clipboard.writeText(resultText);
    showToast("结果已复制到剪贴板");
  } catch (error) {
    console.error("Copy failed:", error);
    showToast("复制失败");
  }
};
</script>

<style lang="scss" scoped>
.cloudflare-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: "Inter", sans-serif;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 40px;

  h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 20px;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

.test-container {
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;

  h2 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.test-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  h3 {
    color: #333;
    font-size: 1.25rem;
    margin-bottom: 8px;
    font-weight: 600;
  }

  p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 16px;
  }
}

.test-btn {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.primary { background: #007bff; color: white; &:hover:not(:disabled) { background: #0056b3; } }
  &.secondary { background: #6c757d; color: white; &:hover:not(:disabled) { background: #545b62; } }
  &.warning { background: #ffc107; color: #212529; &:hover:not(:disabled) { background: #e0a800; } }
  &.danger { background: #dc3545; color: white; &:hover:not(:disabled) { background: #c82333; } }
  &.info { background: #17a2b8; color: white; &:hover:not(:disabled) { background: #138496; } }
  &.success { background: #28a745; color: white; &:hover:not(:disabled) { background: #1e7e34; } }
  &.purple { background: #6f42c1; color: white; &:hover:not(:disabled) { background: #5a32a3; } }
  &.dark { background: #343a40; color: white; &:hover:not(:disabled) { background: #23272b; } }
}

.result-card, .info-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    color: #333;
    margin: 0;
  }

  .status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;

    &.success {
      background: #d4edda;
      color: #155724;
    }

    &.failed {
      background: #f8d7da;
      color: #721c24;
    }
  }
}

.result-details {
  margin-bottom: 20px;

  .detail-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    label {
      font-weight: 600;
      color: #333;
    }

    span {
      color: #666;
      font-family: monospace;
    }
  }
}

.result-actions {
  display: flex;
  gap: 12px;

  button {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
  }

  .clear-btn {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;

    &:hover {
      background: #e9ecef;
    }
  }

  .copy-btn {
    background: #007bff;
    color: white;

    &:hover {
      background: #0056b3;
    }
  }
}

.info-card {
  h3 {
    color: #333;
    margin-bottom: 12px;
  }

  ol, ul {
    margin-bottom: 20px;
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      line-height: 1.5;
      color: #555;
    }
  }

  strong {
    color: #333;
  }
}

@media (max-width: 768px) {
  .test-grid {
    grid-template-columns: 1fr;
  }

  .header h1 {
    font-size: 2rem;
  }

  .test-card, .result-card, .info-card {
    padding: 20px;
  }

  .result-actions {
    flex-direction: column;
  }
}
</style>

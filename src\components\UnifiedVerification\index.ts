/**
 * 统一验证组件导出
 */

export { useUnifiedVerification } from "./useUnifiedVerification";
export { default as UnifiedVerificationDialog } from "./UnifiedVerificationDialog.vue";

export {
  VerificationMode,
  VERIFICATION_SCENES,
  type UnifiedVerificationConfig,
  type UnifiedVerifyResult,
  type VerificationScene,
} from "./useUnifiedVerification";

export { VerificationStatus } from "@/components/CloudFlare/types";

/**
 * CloudFlare 验证组件导出
 * 基于 cocos-ca-h5 项目重构优化
 */

// 组件
import CloudflareVerification from "./CloudflareVerification.vue";
export { CloudflareVerification };

// Composables
export { useCloudflareVerification, usePhoneVerification } from "./useCloudflareVerification";

// 函数调用 API
export {
  initCloudflareVerification,
  verifyCloudflare,
  verifyCloudflareWithRetry,
  quickVerify,
  consumeVerificationToken,
  getVerificationStats,
  clearVerificationStats,
  resetVerificationState,
  isVerificationInitialized,
  getCurrentConfig,
  createVerificationComponent,
  VerificationScenes,
} from "./cloudflareApi";

// 默认导出（函数调用模式）
export { default as CloudflareAPI } from "./cloudflareApi";

// 类型导出
export type {
  VerifyResult,
  CloudflareInfo,
  CloudflareInfoMap,
  CloudflareConfig,
  OperationLockConfig,
  UserData,
  ApiResponse,
  PhoneVerificationParams,
  SmsVerifyParams,
  BindPhoneParams,
  ChangePhoneParams,
  CloudflareController,
  MessageHandler,
  VerifyCallback,
  PlatformInfo,
  VerificationStep,
  VerificationState,
  ComponentEvents,
  ErrorInfo,
  VerificationConfig,
  SceneConfigMap,
  PhoneVerificationResult,
  CloudflareVerificationProps,
  CloudflareVerificationEmits,
  CloudflareApiOptions,
  CloudflareApiResult,
  VerificationStatus,
  VerificationStats,
  AdvancedConfig,
} from "./types";

// 枚举和常量导出
export {
  CloudFlareScene,
  VerifyType,
  SmsType,
  StorageKeys,
  VerificationStatus,
  DEFAULT_SCENE_CONFIG,
} from "./types";

// 工具类导出
export {
  LocalStorage,
  ErrorHandler,
  PhoneValidator,
  CountdownManager,
  UrlUtils,
  DeviceUtils,
  debounce,
  throttle,
} from "./utils";

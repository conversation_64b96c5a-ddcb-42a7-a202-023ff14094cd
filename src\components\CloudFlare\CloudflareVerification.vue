<template>
  <!-- CloudFlare 验证对话框 -->
  <div v-if="showVerifyDialog" class="cloudflare-verify-overlay" @click="handleOverlayClick">
    <div class="verify-dialog" @click.stop>
      <div class="verify-header">
        <h3>{{ texts.security_verification }}</h3>
        <button @click="closeVerify" class="close-btn" :disabled="!canInterrupt">×</button>
      </div>

      <!-- 加载状态 -->
      <div v-if="verificationStatus === 'loading'" class="loading-content">
        <div class="loading-spinner"></div>
        <p>{{ texts.loading_verification }}</p>
      </div>

      <!-- 验证内容 -->
      <div v-else class="verify-content">
        <iframe
          ref="verifyFrame"
          :src="verifyUrl"
          class="verify-iframe"
          frameborder="0"
          @load="onFrameLoad"
          @error="onFrameError"
        ></iframe>
      </div>

      <!-- 错误信息 -->
      <div v-if="showError" class="error-message">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ errorMessage }}</div>
        <button v-if="enableRetry" @click="retryVerification" class="retry-btn">
          {{ texts.retry }}
        </button>
      </div>

      <!-- 状态指示器 -->
      <div v-if="showStatusIndicator" class="status-indicator">
        <div class="status-dot" :class="statusClass"></div>
        <span class="status-text">{{ statusText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import type {
  CloudFlareScene,
  VerifyResult,
  CloudflareConfig,
  AdvancedConfig,
  VerificationStatus,
} from "./types";
import { useCloudflareVerification } from "./useCloudflareVerification";

// Props 定义
interface Props {
  config?: CloudflareConfig;
  advancedConfig?: AdvancedConfig;
  enableAutoClose?: boolean;
  enableRetry?: boolean;
  showStatusIndicator?: boolean;
  texts?: Record<string, string>;
}

// Emits 定义
interface Emits {
  (e: "success", result: VerifyResult): void;
  (e: "cancel"): void;
  (e: "error", error: Error): void;
  (e: "statusChange", status: VerificationStatus): void;
}

const props = withDefaults(defineProps<Props>(), {
  enableAutoClose: false,
  enableRetry: true,
  showStatusIndicator: true,
  texts: () => ({
    security_verification: "安全验证",
    loading_verification: "正在加载验证...",
    retry: "重试",
    verification_failed: "验证失败",
    verification_timeout: "验证超时",
    verification_cancelled: "验证已取消",
    verification_success: "验证成功",
  }),
});

const emit = defineEmits<Emits>();

// CloudFlare 验证实例
const cloudflareVerification = useCloudflareVerification(props.config, props.advancedConfig);

// 解构状态和方法
const {
  showVerifyDialog,
  verifyUrl,
  verificationStatus,
  canInterrupt,
  getVerifyToken,
  closeVerify,
  onVerifySuccess,
  onVerifyFailed,
  onVerifyTimeout,
  resetVerification,
} = cloudflareVerification;

// 本地状态
const verifyFrame = ref<HTMLIFrameElement>();
const showError = ref(false);
const errorMessage = ref("");
const currentScene = ref<CloudFlareScene | null>(null);
const retryCount = ref(0);
const maxRetries = ref(3);

// 计算属性
const statusClass = computed(() => {
  switch (verificationStatus.value) {
    case "loading":
      return "status-loading";
    case "verifying":
      return "status-verifying";
    case "success":
      return "status-success";
    case "failed":
    case "timeout":
      return "status-error";
    case "cancelled":
      return "status-cancelled";
    default:
      return "status-idle";
  }
});

const statusText = computed(() => {
  switch (verificationStatus.value) {
    case "loading":
      return props.texts.loading_verification;
    case "verifying":
      return "正在验证...";
    case "success":
      return props.texts.verification_success;
    case "failed":
      return props.texts.verification_failed;
    case "timeout":
      return props.texts.verification_timeout;
    case "cancelled":
      return props.texts.verification_cancelled;
    default:
      return "";
  }
});

// 方法
const handleOverlayClick = () => {
  // CloudFlare 验证对话框背景点击
  if (props.enableAutoClose && canInterrupt.value) {
    closeVerify();
  }
};

const onFrameLoad = () => {
  console.log("CloudFlare 验证框架加载完成");
  showError.value = false;
  errorMessage.value = "";
};

const onFrameError = () => {
  console.error("CloudFlare 验证框架加载失败");
  showError.value = true;
  errorMessage.value = "验证服务加载失败，请刷新重试";
  emit("error", new Error("验证框架加载失败"));
};

const retryVerification = async () => {
  if (retryCount.value >= maxRetries.value) {
    errorMessage.value = "重试次数已达上限";
    return;
  }

  if (!currentScene.value) {
    errorMessage.value = "无法重试，场景信息丢失";
    return;
  }

  retryCount.value++;
  showError.value = false;
  errorMessage.value = "";

  try {
    await startCloudflareVerification(currentScene.value);
  } catch (error) {
    console.error("重试验证失败:", error);
  }
};

// 公开方法
const startCloudflareVerification = async (scene: CloudFlareScene) => {
  try {
    currentScene.value = scene;
    retryCount.value = 0;
    showError.value = false;
    errorMessage.value = "";

    const result = await getVerifyToken(scene);

    if (result.code === 0) {
      emit("success", result);
    } else {
      const error = new Error(`验证失败，代码: ${result.code}`);
      emit("error", error);
    }

    return result;
  } catch (error) {
    console.error("CloudFlare 验证失败:", error);
    emit("error", error as Error);
    throw error;
  }
};

// 监听验证状态变化
watch(verificationStatus, (newStatus) => {
  emit("statusChange", newStatus);

  // 根据状态更新错误信息
  if (newStatus === "failed") {
    showError.value = true;
    errorMessage.value = props.texts.verification_failed;
  } else if (newStatus === "timeout") {
    showError.value = true;
    errorMessage.value = props.texts.verification_timeout;
  } else if (newStatus === "success") {
    showError.value = false;
    errorMessage.value = "";
  }
});

// 暴露方法
defineExpose({
  startCloudflareVerification,
  closeVerify,
  retryVerification,
  resetVerification,
});
</script>

<style scoped>
/* CloudFlare 验证对话框样式 */
.cloudflare-verify-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.verify-dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.verify-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.verify-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover:not(:disabled) {
  background-color: #f0f0f0;
  color: #333;
}

.close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading-content {
  padding: 40px 24px;
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.verify-content {
  padding: 24px;
  min-height: 300px;
}

.verify-iframe {
  width: 100%;
  height: 300px;
  border: none;
  border-radius: 8px;
}

.error-message {
  padding: 20px 24px;
  background-color: #fee;
  color: #c33;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.error-text {
  flex: 1;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #0056b3;
}

.status-indicator {
  padding: 12px 24px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-idle {
  background: #6c757d;
}
.status-loading {
  background: #007bff;
}
.status-verifying {
  background: #ffc107;
}
.status-success {
  background: #28a745;
}
.status-error {
  background: #dc3545;
}
.status-cancelled {
  background: #6c757d;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .verify-dialog {
    width: 95%;
    margin: 20px;
  }

  .verify-content {
    padding: 20px;
  }

  .verify-header {
    padding: 16px 20px;
  }
}
</style>

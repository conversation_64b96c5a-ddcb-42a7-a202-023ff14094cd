<template>
  <div class="cloudflare-test-page">
    <div class="header">
      <h1>CloudFlare 验证组件测试页面</h1>
      <p class="subtitle">基于 cocos-ca-h5 项目重构优化的 CloudFlare 验证系统</p>
    </div>

    <!-- 配置面板 -->
    <div class="config-panel">
      <h2>配置设置</h2>
      <div class="config-grid">
        <div class="config-item">
          <label>环境模式:</label>
          <select v-model="selectedMode" @change="updateConfig">
            <option value="PRE">生产环境</option>
            <option value="DEV">开发环境</option>
            <option value="TEST">测试环境</option>
          </select>
        </div>
        <div class="config-item">
          <label>启用统计:</label>
          <input type="checkbox" v-model="advancedConfig.enableStats" />
        </div>
        <div class="config-item">
          <label>启用重试:</label>
          <input type="checkbox" v-model="advancedConfig.enableRetry" />
        </div>
        <div class="config-item">
          <label>启用日志:</label>
          <input type="checkbox" v-model="advancedConfig.enableLogging" />
        </div>
      </div>
    </div>

    <!-- 测试场景 -->
    <div class="test-scenarios">
      <h2>测试场景</h2>
      <div class="scenario-grid">
        <div
          v-for="scenario in testScenarios"
          :key="scenario.scene"
          class="scenario-card"
          :class="{ active: currentScenario === scenario.scene }"
          @click="selectScenario(scenario.scene)"
        >
          <h3>{{ scenario.name }}</h3>
          <p>{{ scenario.description }}</p>
          <div class="scenario-actions">
            <button @click.stop="testScenario(scenario.scene)" :disabled="loading">
              {{ loading && currentScenario === scenario.scene ? "测试中..." : "开始测试" }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <h2>测试结果</h2>
      <div class="results-container">
        <div
          v-for="(result, index) in testResults"
          :key="index"
          class="result-item"
          :class="result.success ? 'success' : 'error'"
        >
          <div class="result-header">
            <span class="result-scene">{{ result.sceneName }}</span>
            <span class="result-time">{{ result.timestamp }}</span>
            <span class="result-status">{{ result.success ? "成功" : "失败" }}</span>
          </div>
          <div class="result-details">
            <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
          </div>
        </div>
      </div>
      <button @click="clearResults" class="clear-results-btn">清除结果</button>
    </div>

    <!-- 性能监控 -->
    <div class="performance-monitor">
      <h2>性能监控</h2>
      <div class="performance-grid">
        <div class="performance-item">
          <label>总测试次数:</label>
          <span>{{ performanceStats.totalTests }}</span>
        </div>
        <div class="performance-item">
          <label>成功次数:</label>
          <span>{{ performanceStats.successCount }}</span>
        </div>
        <div class="performance-item">
          <label>失败次数:</label>
          <span>{{ performanceStats.failureCount }}</span>
        </div>
        <div class="performance-item">
          <label>平均耗时:</label>
          <span>{{ performanceStats.averageDuration.toFixed(2) }}ms</span>
        </div>
        <div class="performance-item">
          <label>成功率:</label>
          <span>{{ performanceStats.successRate.toFixed(1) }}%</span>
        </div>
      </div>
    </div>

    <!-- CloudFlare 验证组件 -->
    <CloudflareVerification
      ref="verificationRef"
      :config="cloudflareConfig"
      :advanced-config="advancedConfig"
      :enable-retry="true"
      :show-status-indicator="true"
      @success="onVerificationSuccess"
      @cancel="onVerificationCancel"
      @error="onVerificationError"
      @status-change="onStatusChange"
    />

    <!-- 调试信息 -->
    <div class="debug-panel">
      <h2>调试信息</h2>
      <div class="debug-content">
        <div class="debug-item">
          <label>当前状态:</label>
          <span class="status-badge" :class="currentStatus">{{ currentStatus }}</span>
        </div>
        <div class="debug-item">
          <label>操作锁定:</label>
          <span class="status-badge" :class="isOperationLocked ? 'locked' : 'unlocked'">
            {{ isOperationLocked ? "是" : "否" }}
          </span>
        </div>
        <div class="debug-item">
          <label>最后Token:</label>
          <span class="token-display">{{ lastToken || "无" }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import {
  CloudflareAPI,
  CloudflareVerification,
  useCloudflareVerification,
  CloudFlareScene,
  type CloudflareConfig,
  type AdvancedConfig,
  type VerificationStatus,
} from "@/components/CloudFlare";

// 配置
const selectedMode = ref("PRE");
const cloudflareConfig = reactive<CloudflareConfig>({
  ActivityPageHost: {
    PRE: "https://example.com/",
    DEV: "https://dev.example.com/",
    TEST: "https://test.example.com/",
  },
  CloudflareVerify_SITE_KEY: {
    PRE: "0x4AAAAAABr6liO_iAPr4Zx_",
    DEV: "0x4AAAAAABr6liO_iAPr4Zx_",
    TEST: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  debug_mode_main: "PRE",
});

const advancedConfig = reactive<AdvancedConfig>({
  enableStats: true,
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableLogging: true,
  logLevel: "info",
});

// 状态
const loading = ref(false);
const currentStatus = ref<VerificationStatus>("idle" as VerificationStatus);
const currentScenario = ref<CloudFlareScene | null>(null);

// 组件引用
const verificationRef = ref();

// CloudFlare 验证实例
const cloudflareVerification = useCloudflareVerification(cloudflareConfig, advancedConfig);
const { isOperationLocked, lastToken } = cloudflareVerification;

// 测试场景
const testScenarios = [
  {
    scene: CloudFlareScene.LOGIN_SUBMIT,
    name: "登录验证",
    description: "用户登录时的安全验证",
  },
  {
    scene: CloudFlareScene.LOGIN_PHONE_GET_CODE,
    name: "获取验证码",
    description: "手机号获取验证码时的验证",
  },
  {
    scene: CloudFlareScene.FORGET_PW_SUBMIT,
    name: "忘记密码",
    description: "重置密码时的安全验证",
  },
  {
    scene: CloudFlareScene.WITHDRAWAL_SUBMIT,
    name: "提现验证",
    description: "用户提现时的安全验证",
  },
  {
    scene: CloudFlareScene.KYC_SUBMIT,
    name: "KYC验证",
    description: "身份认证时的安全验证",
  },
];

// 测试结果
interface TestResult {
  sceneName: string;
  scene: CloudFlareScene;
  success: boolean;
  data: any;
  timestamp: string;
  duration: number;
}

const testResults = reactive<TestResult[]>([]);

// 性能统计
const performanceStats = computed(() => {
  const total = testResults.length;
  const success = testResults.filter((r) => r.success).length;
  const failure = total - success;
  const avgDuration = total > 0 ? testResults.reduce((sum, r) => sum + r.duration, 0) / total : 0;
  const successRate = total > 0 ? (success / total) * 100 : 0;

  return {
    totalTests: total,
    successCount: success,
    failureCount: failure,
    averageDuration: avgDuration,
    successRate,
  };
});

// 方法
const updateConfig = () => {
  cloudflareConfig.debug_mode_main = selectedMode.value;

  // 重新初始化 API
  if (CloudflareAPI.isInitialized()) {
    CloudflareAPI.init(cloudflareConfig, advancedConfig);
  }
};

const selectScenario = (scene: CloudFlareScene) => {
  currentScenario.value = scene;
};

const testScenario = async (scene: CloudFlareScene) => {
  const startTime = Date.now();
  loading.value = true;
  currentScenario.value = scene;

  try {
    const result = await CloudflareAPI.verify(scene, {
      timeout: 30000,
    });

    const duration = Date.now() - startTime;
    const sceneName = testScenarios.find((s) => s.scene === scene)?.name || scene;

    testResults.unshift({
      sceneName,
      scene,
      success: result.code === 0,
      data: result,
      timestamp: new Date().toLocaleTimeString(),
      duration,
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    const sceneName = testScenarios.find((s) => s.scene === scene)?.name || scene;

    testResults.unshift({
      sceneName,
      scene,
      success: false,
      data: { error: error.message },
      timestamp: new Date().toLocaleTimeString(),
      duration,
    });
  } finally {
    loading.value = false;
  }
};

const clearResults = () => {
  testResults.splice(0);
};

const onVerificationSuccess = (result: any) => {
  console.log("验证成功:", result);
};

const onVerificationCancel = () => {
  console.log("验证取消");
};

const onVerificationError = (error: Error) => {
  console.error("验证错误:", error);
};

const onStatusChange = (status: VerificationStatus) => {
  currentStatus.value = status;
};

// 初始化
onMounted(() => {
  if (!CloudflareAPI.isInitialized()) {
    CloudflareAPI.init(cloudflareConfig, advancedConfig);
  }
});
</script>

<style scoped>
.cloudflare-test-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
}

.config-panel,
.test-scenarios,
.test-results,
.performance-monitor,
.debug-panel {
  margin-bottom: 30px;
  padding: 25px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.config-panel h2,
.test-scenarios h2,
.test-results h2,
.performance-monitor h2,
.debug-panel h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.5em;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.config-grid,
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.config-item,
.performance-item,
.debug-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.config-item label,
.performance-item label,
.debug-item label {
  font-weight: 500;
  color: #555;
}

.config-item select,
.config-item input {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.scenario-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.scenario-card {
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.scenario-card:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

.scenario-card.active {
  border-color: #007bff;
  background: #e3f2fd;
}

.scenario-card h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.2em;
}

.scenario-card p {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 0.9em;
}

.scenario-actions button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.scenario-actions button:hover:not(:disabled) {
  background: #0056b3;
}

.scenario-actions button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.results-container {
  max-height: 500px;
  overflow-y: auto;
}

.result-item {
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #dee2e6;
}

.result-item.success {
  border-left: 4px solid #28a745;
}

.result-item.error {
  border-left: 4px solid #dc3545;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.result-scene {
  font-weight: 600;
  color: #333;
}

.result-time {
  color: #6c757d;
  font-size: 0.9em;
}

.result-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 500;
}

.result-item.success .result-status {
  background: #d4edda;
  color: #155724;
}

.result-item.error .result-status {
  background: #f8d7da;
  color: #721c24;
}

.result-details {
  padding: 16px;
  background: white;
}

.result-details pre {
  margin: 0;
  font-size: 12px;
  overflow-x: auto;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
}

.clear-results-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 15px;
}

.clear-results-btn:hover {
  background: #c82333;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.idle {
  background: #6c757d;
  color: white;
}
.status-badge.loading {
  background: #007bff;
  color: white;
}
.status-badge.verifying {
  background: #ffc107;
  color: black;
}
.status-badge.success {
  background: #28a745;
  color: white;
}
.status-badge.failed {
  background: #dc3545;
  color: white;
}
.status-badge.locked {
  background: #dc3545;
  color: white;
}
.status-badge.unlocked {
  background: #28a745;
  color: white;
}

.token-display {
  font-family: "Courier New", monospace;
  font-size: 12px;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.debug-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}
</style>

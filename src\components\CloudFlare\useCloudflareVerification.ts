/**
 * CloudFlare 验证 Composable
 * 基于 cocos-ca-h5 项目重构优化
 * 提供 Vue 3 风格的 CloudFlare 验证功能
 */

import { ref, reactive, computed, onUnmounted, nextTick } from "vue";
import type {
  VerifyResult,
  CloudflareConfig,
  CloudflareInfo,
  CloudflareInfoMap,
  OperationLockConfig,
  VerifyType,
  VerificationState,
  PhoneVerificationResult,
  MessageHandler,
  VerifyCallback,
  PlatformInfo,
  VerificationStats,
  AdvancedConfig,
} from "./types";
import { DEFAULT_SCENE_CONFIG, CloudFlareScene, VerificationStatus } from "./types";
import { ErrorHandler, PhoneValidator, CountdownManager, UrlUtils, LocalStorage } from "./utils";

/**
 * 简单的通知函数
 * @param message 消息内容
 */
const showNotification = (message: string) => {
  // 尝试使用 vant 的 showToast，如果不可用则使用 console.log
  try {
    // 动态导入 vant 的 showToast
    import("vant")
      .then(({ showToast }) => {
        showToast(message);
      })
      .catch(() => {
        console.log("📢 通知:", message);
      });
  } catch {
    console.log("📢 通知:", message);
  }
};

/**
 * 获取平台信息
 * @returns 平台信息对象
 */
const getPlatformInfo = (): PlatformInfo => {
  const userAgent = navigator.userAgent.toLowerCase();
  const platform = navigator.platform.toLowerCase();

  let os: PlatformInfo["os"] = "web";

  if (platform.includes("win")) {
    os = "windows";
  } else if (platform.includes("mac")) {
    os = "macos";
  } else if (userAgent.includes("android")) {
    os = "android";
  } else if (userAgent.includes("iphone") || userAgent.includes("ipad")) {
    os = "ios";
  } else if (platform.includes("linux")) {
    os = "linux";
  }

  return {
    isNative: false, // Web 环境下始终为 false
    os,
    userAgent: navigator.userAgent,
  };
};

/**
 * CloudFlare 验证状态管理
 * 基于 cocos-ca-h5 的 CloudflareCtrl 重构
 */
export function useCloudflareVerification(
  config?: CloudflareConfig,
  advancedConfig?: AdvancedConfig
) {
  // 响应式状态
  const isLoading = ref(false);
  const showVerifyDialog = ref(false);
  const verifyUrl = ref("");
  const currentCallback = ref<VerifyCallback | null>(null);
  const messageHandler = ref<MessageHandler | null>(null);
  const operationLocked = ref(false);
  const lockTimeout = ref<NodeJS.Timeout | null>(null);
  const verificationStatus = ref<VerificationStatus>(VerificationStatus.IDLE);
  const canInterrupt = ref(false);
  const interruptTimer = ref<NodeJS.Timeout | null>(null);

  // CloudFlare 状态（基于 cocos-ca-h5 的实现）
  const cloudflareState = reactive({
    lastScene: CloudFlareScene.NONE,
    lastToken: "",
    cfInfoMap: {
      SCENE_LOGIN: {
        mode: "managed",
        sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
      },
      SCENE_GET_CODE: {
        mode: "invisible",
        sitekey: "0x4AAAAAABr6n02z8VbwKkph",
      },
    } as CloudflareInfoMap,
  });

  // 验证统计信息
  const stats = reactive<VerificationStats>({
    totalAttempts: 0,
    successCount: 0,
    failureCount: 0,
    averageDuration: 0,
    lastAttemptTime: 0,
  });

  // 操作锁配置
  const lockConfig: OperationLockConfig = {
    duration: 3000,
    enabled: true,
  };

  // 默认配置
  const defaultConfig: CloudflareConfig = {
    ActivityPageHost: {
      PRE: "https://example.com/",
      DEV: "https://dev.example.com/",
      TEST: "https://test.example.com/",
    },
    CloudflareVerify_SITE_KEY: {
      PRE: "0x4AAAAAABr6liO_iAPr4Zx_",
      DEV: "0x4AAAAAABr6liO_iAPr4Zx_",
      TEST: "0x4AAAAAABr6liO_iAPr4Zx_",
    },
    debug_mode_main: "PRE",
  };

  // 默认高级配置
  const defaultAdvancedConfig: AdvancedConfig = {
    enableStats: true,
    enableRetry: false,
    maxRetries: 3,
    retryDelay: 1000,
    enableLogging: true,
    logLevel: "info",
  };

  const currentConfig = config || defaultConfig;
  const currentAdvancedConfig = advancedConfig || defaultAdvancedConfig;

  // 计算属性
  const isOperationLocked = computed(() => operationLocked.value);
  const platformInfo = computed(() => getPlatformInfo());

  /**
   * 设置操作锁（基于 cocos-ca-h5 的 TimeLock 实现）
   * @param locked 是否锁定
   * @param duration 锁定时长（毫秒）
   */
  const setOperationLock = (locked: boolean, duration = lockConfig.duration) => {
    if (!lockConfig.enabled) return;

    operationLocked.value = locked;

    if (lockTimeout.value) {
      clearTimeout(lockTimeout.value);
      lockTimeout.value = null;
    }

    if (locked) {
      lockTimeout.value = setTimeout(() => {
        operationLocked.value = false;
        lockTimeout.value = null;
        if (currentAdvancedConfig.enableLogging) {
          console.log("CloudFlare 操作锁已自动解除");
        }
      }, duration);
    }
  };

  /**
   * 设置中断计时器
   * @param duration 计时时长（毫秒）
   */
  const setInterruptTimer = (duration = 10000) => {
    canInterrupt.value = false;

    if (interruptTimer.value) {
      clearTimeout(interruptTimer.value);
    }

    interruptTimer.value = setTimeout(() => {
      canInterrupt.value = true;
      if (currentAdvancedConfig.enableLogging) {
        console.log("CloudFlare 验证可以中断");
      }
    }, duration);
  };

  /**
   * 取消中断计时器
   */
  const cancelInterruptTimer = () => {
    if (interruptTimer.value) {
      clearTimeout(interruptTimer.value);
      interruptTimer.value = null;
    }
    canInterrupt.value = true;
  };

  /**
   * 更新验证统计信息
   * @param success 是否成功
   * @param duration 耗时
   */
  const updateStats = (success: boolean, duration: number) => {
    if (!currentAdvancedConfig.enableStats) return;

    stats.totalAttempts++;
    stats.lastAttemptTime = Date.now();

    if (success) {
      stats.successCount++;
    } else {
      stats.failureCount++;
    }

    // 计算平均耗时
    stats.averageDuration =
      (stats.averageDuration * (stats.totalAttempts - 1) + duration) / stats.totalAttempts;

    // 保存到本地存储
    LocalStorage.setItem("cloudflare_stats", stats);
  };

  /**
   * 获取验证 token（基于 cocos-ca-h5 的实现）
   * @param scene 验证场景
   * @param callback 可选的回调函数
   * @returns Promise<VerifyResult>
   */
  const getVerifyToken = async (
    scene: CloudFlareScene,
    callback?: VerifyCallback
  ): Promise<VerifyResult> => {
    const startTime = Date.now();
    verificationStatus.value = VerificationStatus.LOADING;

    return new Promise((resolve) => {
      // 检查操作锁
      if (isOperationLocked.value) {
        const result = { token: "", code: 1 };
        verificationStatus.value = VerificationStatus.FAILED;
        updateStats(false, Date.now() - startTime);
        resolve(result);
        callback?.(result);
        return;
      }

      // 检查是否已有验证对话框显示
      if (showVerifyDialog.value) {
        const result = { token: "", code: 1 };
        verificationStatus.value = VerificationStatus.FAILED;
        updateStats(false, Date.now() - startTime);
        resolve(result);
        callback?.(result);
        return;
      }

      // 锁定操作
      setOperationLock(true);

      // 清除之前的 token 数据
      cloudflareState.lastScene = CloudFlareScene.NONE;
      cloudflareState.lastToken = "";

      // 设置当前回调
      currentCallback.value = (result: VerifyResult) => {
        const duration = Date.now() - startTime;
        updateStats(result.code === 0, duration);
        resolve(result);
        callback?.(result);
      };

      // 获取 CloudFlare 信息
      const cfInfo = cloudflareState.cfInfoMap[scene];
      if (!cfInfo) {
        setOperationLock(false);
        verificationStatus.value = VerificationStatus.FAILED;
        const errorMsg = "验证配置未找到";
        showNotification(errorMsg);
        ErrorHandler.logError(new Error(errorMsg), "getVerifyToken");

        const result = { token: "", code: 1 };
        updateStats(false, Date.now() - startTime);
        resolve(result);
        callback?.(result);
        return;
      }

      const appearance = "always";
      const autoClose = cfInfo.mode === "invisible";
      const mode = currentConfig.debug_mode_main || "PRE";
      const pageHost = currentConfig.ActivityPageHost[mode];
      const platform = platformInfo.value;

      // 构建验证 URL
      verifyUrl.value = UrlUtils.buildVerifyUrl(pageHost, cfInfo.sitekey, appearance, {
        isNative: platform.isNative ? "1" : "0",
        os: platform.os,
      });

      // 显示验证对话框
      showVerifyDialog.value = true;
      verificationStatus.value = VerificationStatus.VERIFYING;

      // 设置中断计时器
      setInterruptTimer();

      if (currentAdvancedConfig.enableLogging) {
        console.log(`CloudFlare 验证开始: ${scene}`, {
          cfInfo,
          verifyUrl: verifyUrl.value,
          platform,
        });
      }
    });
  };

  /**
   * 消费 token（获取并清空）
   * @returns token 数据或 null
   */
  const consumeToken = () => {
    if (cloudflareState.lastScene && cloudflareState.lastToken) {
      const result = {
        scene: cloudflareState.lastScene,
        token: cloudflareState.lastToken,
      };
      cloudflareState.lastScene = CloudFlareScene.NONE;
      cloudflareState.lastToken = "";

      if (currentAdvancedConfig.enableLogging) {
        console.log("CloudFlare token 已消费", result);
      }

      return result;
    }
    return null;
  };

  /**
   * 处理验证成功（基于 cocos-ca-h5 的实现）
   * @param token 验证 token
   */
  const onVerifySuccess = (token: string) => {
    if (currentAdvancedConfig.enableLogging) {
      console.log("CloudFlare 验证成功", token);
    }

    cancelInterruptTimer();
    verificationStatus.value = VerificationStatus.SUCCESS;

    // 使用 nextTick 确保状态更新
    nextTick(() => {
      if (!token) {
        onVerifyFailed();
        return;
      }

      // 记录 token 数据
      cloudflareState.lastScene = cloudflareState.lastScene;
      cloudflareState.lastToken = token;

      // 调用回调
      currentCallback.value?.({ token, code: 0 });
      closeVerify();
      setOperationLock(false);
    });
  };

  /**
   * 处理验证失败（基于 cocos-ca-h5 的实现）
   */
  const onVerifyFailed = () => {
    if (currentAdvancedConfig.enableLogging) {
      console.log("CloudFlare 验证失败");
    }

    cancelInterruptTimer();
    verificationStatus.value = VerificationStatus.FAILED;

    // 显示错误提示
    showNotification("验证失败，请重试");

    // 调用回调
    currentCallback.value?.({ token: "", code: 2 });
    closeVerify();
    setOperationLock(false);
  };

  /**
   * 处理验证超时
   */
  const onVerifyTimeout = () => {
    if (currentAdvancedConfig.enableLogging) {
      console.log("CloudFlare 验证超时");
    }

    cancelInterruptTimer();
    verificationStatus.value = VerificationStatus.TIMEOUT;

    // 显示超时提示
    showNotification("验证超时，请重试");

    // 调用回调
    currentCallback.value?.({ token: "", code: 2 });
    closeVerify();
    setOperationLock(false);
  };

  /**
   * 关闭验证对话框（基于 cocos-ca-h5 的实现）
   */
  const closeVerify = () => {
    if (!canInterrupt.value && showVerifyDialog.value) {
      if (currentAdvancedConfig.enableLogging) {
        console.log("CloudFlare 验证不能中断");
      }
      return;
    }

    showVerifyDialog.value = false;
    verificationStatus.value = VerificationStatus.CANCELLED;

    if (currentCallback.value) {
      currentCallback.value({ token: "", code: 2 });
      currentCallback.value = null;
    }

    cancelInterruptTimer();
    setOperationLock(false);

    if (currentAdvancedConfig.enableLogging) {
      console.log("CloudFlare 验证对话框已关闭");
    }
  };

  /**
   * 处理 iframe 消息（基于 cocos-ca-h5 的实现）
   * @param event 消息事件
   */
  const onMessageHandler = (event: MessageEvent) => {
    // 验证消息来源（可选的安全检查）
    if (currentAdvancedConfig.enableLogging) {
      console.log("CloudFlare 收到消息", event.data);
    }

    switch (event.data.type) {
      case "onTurnstileSuccess":
        onVerifySuccess(event.data.token);
        break;
      case "onTurnstileError":
        onVerifyFailed();
        break;
      case "onTurnstileTimeout":
        onVerifyTimeout();
        break;
      case "onTurnstileUnsupported":
        onVerifyFailed();
        break;
      default:
        if (currentAdvancedConfig.enableLogging) {
          console.log("CloudFlare 未知消息类型", event.data.type);
        }
        break;
    }
  };

  /**
   * 初始化消息监听
   */
  const initializeMessageListener = () => {
    messageHandler.value = onMessageHandler;
    window.addEventListener("message", messageHandler.value);

    if (currentAdvancedConfig.enableLogging) {
      console.log("CloudFlare 消息监听器已初始化");
    }
  };

  /**
   * 清理资源
   */
  const cleanup = () => {
    if (messageHandler.value) {
      window.removeEventListener("message", messageHandler.value);
      messageHandler.value = null;
    }

    if (lockTimeout.value) {
      clearTimeout(lockTimeout.value);
      lockTimeout.value = null;
    }

    cancelInterruptTimer();
    CountdownManager.clearAll();

    if (currentAdvancedConfig.enableLogging) {
      console.log("CloudFlare 资源已清理");
    }
  };

  /**
   * 重置验证状态
   */
  const resetVerification = () => {
    cloudflareState.lastScene = CloudFlareScene.NONE;
    cloudflareState.lastToken = "";
    verificationStatus.value = VerificationStatus.IDLE;
    showVerifyDialog.value = false;
    currentCallback.value = null;
    setOperationLock(false);
    cancelInterruptTimer();
  };

  /**
   * 获取验证统计信息
   */
  const getStats = () => {
    return { ...stats };
  };

  /**
   * 清除验证统计信息
   */
  const clearStats = () => {
    Object.assign(stats, {
      totalAttempts: 0,
      successCount: 0,
      failureCount: 0,
      averageDuration: 0,
      lastAttemptTime: 0,
    });
    LocalStorage.removeItem("cloudflare_stats");
  };

  // 生命周期
  onUnmounted(() => {
    cleanup();
  });

  // 初始化
  initializeMessageListener();

  // 从本地存储恢复统计信息
  if (currentAdvancedConfig.enableStats) {
    const savedStats = LocalStorage.getItem("cloudflare_stats", null);
    if (savedStats) {
      Object.assign(stats, savedStats);
    }
  }

  return {
    // 状态
    isLoading,
    showVerifyDialog,
    verifyUrl,
    isOperationLocked,
    verificationStatus: computed(() => verificationStatus.value),
    canInterrupt: computed(() => canInterrupt.value),
    lastScene: computed(() => cloudflareState.lastScene),
    lastToken: computed(() => cloudflareState.lastToken),
    platformInfo,
    stats: computed(() => getStats()),

    // 方法
    getVerifyToken,
    consumeToken,
    closeVerify,
    setOperationLock,
    resetVerification,
    getStats,
    clearStats,

    // 内部方法（用于组件）
    onVerifySuccess,
    onVerifyFailed,
    onVerifyTimeout,
    onMessageHandler,
    setInterruptTimer,
    cancelInterruptTimer,
  };
}

/**
 * 手机验证 Composable
 */
export function usePhoneVerification() {
  // 验证状态
  const verificationState = reactive<VerificationState>({
    visible: false,
    currentStep: "phone",
    phoneNumber: "",
    verificationCode: "",
    phoneError: false,
    phoneErrorMessage: "",
    codeError: false,
    codeErrorMessage: "",
    isSending: false,
    isSubmitting: false,
    isCountingDown: false,
    countdown: 60,
  });

  /**
   * 显示验证对话框
   * @param verifyType 验证类型
   * @param initialPhone 初始手机号
   */
  const showVerification = (verifyType: VerifyType, initialPhone?: string) => {
    verificationState.visible = true;
    verificationState.currentStep = "phone";
    verificationState.phoneNumber = initialPhone || "";
    clearErrors();
  };

  /**
   * 关闭验证对话框
   */
  const closeVerification = () => {
    verificationState.visible = false;
    verificationState.currentStep = "phone";
    verificationState.phoneNumber = "";
    verificationState.verificationCode = "";
    clearErrors();
    CountdownManager.stop("sms_countdown");
  };

  /**
   * 清除错误状态
   */
  const clearErrors = () => {
    verificationState.phoneError = false;
    verificationState.phoneErrorMessage = "";
    verificationState.codeError = false;
    verificationState.codeErrorMessage = "";
  };

  /**
   * 验证手机号
   * @returns 是否验证通过
   */
  const validatePhone = (): boolean => {
    const phone = verificationState.phoneNumber.trim();

    if (!phone) {
      verificationState.phoneError = true;
      verificationState.phoneErrorMessage = "手机号不能为空";
      return false;
    }

    if (!PhoneValidator.isValidPhilippinePhone(phone)) {
      verificationState.phoneError = true;
      verificationState.phoneErrorMessage = "请输入正确的手机号";
      return false;
    }

    verificationState.phoneNumber = PhoneValidator.normalizePhone(phone);
    verificationState.phoneError = false;
    verificationState.phoneErrorMessage = "";
    return true;
  };

  /**
   * 发送验证码
   * @param verifyType 验证类型
   * @param cloudflareVerification CloudFlare 验证实例
   */
  const sendVerificationCode = async (
    verifyType: VerifyType,
    cloudflareVerification?: ReturnType<typeof useCloudflareVerification>
  ) => {
    if (verificationState.isSending || verificationState.isCountingDown) return;

    if (!validatePhone()) return;

    verificationState.isSending = true;

    try {
      // 这里应该调用实际的发送短信 API
      // 示例实现
      await new Promise((resolve) => setTimeout(resolve, 1000));

      showNotification("验证码发送成功");
      verificationState.currentStep = "code";

      // 开始倒计时
      startCountdown();
    } catch (error) {
      ErrorHandler.logError(error, "sendVerificationCode");
      showNotification("发送验证码失败");
    } finally {
      verificationState.isSending = false;
    }
  };

  /**
   * 开始倒计时
   */
  const startCountdown = () => {
    verificationState.isCountingDown = true;

    CountdownManager.start(
      "sms_countdown",
      60,
      (remaining) => {
        verificationState.countdown = remaining;
      },
      () => {
        verificationState.isCountingDown = false;
      }
    );
  };

  /**
   * 验证验证码
   * @param verifyType 验证类型
   * @returns 验证结果
   */
  const verifyCode = async (verifyType: VerifyType): Promise<PhoneVerificationResult> => {
    const code = verificationState.verificationCode.trim();

    if (code.length !== 6) {
      verificationState.codeError = true;
      verificationState.codeErrorMessage = "请输入6位验证码";
      return { success: false, error: "验证码格式错误", verifyType };
    }

    verificationState.isSubmitting = true;

    try {
      // 这里应该调用实际的验证 API
      // 示例实现
      await new Promise((resolve) => setTimeout(resolve, 1000));

      return {
        success: true,
        data: {
          phone: verificationState.phoneNumber,
          code,
        },
        verifyType,
      };
    } catch (error) {
      ErrorHandler.logError(error, "verifyCode");
      verificationState.codeError = true;
      verificationState.codeErrorMessage = "验证失败，请重试";
      return { success: false, error: "验证失败", verifyType };
    } finally {
      verificationState.isSubmitting = false;
    }
  };

  return {
    // 状态
    verificationState,

    // 方法
    showVerification,
    closeVerification,
    clearErrors,
    validatePhone,
    sendVerificationCode,
    verifyCode,
    startCountdown,
  };
}

import { isPlainObject, omitBy } from "lodash-es";
import { showToast } from "vant";
import enTranslations from "@/utils/I18n/en.json";
/**
 * 可以再次单独配置的属性
 * 单独再次配置的属性会覆盖全局属性
 */
export const defaultConfig = {
  transformResult: null,
  baseURL: null,
  error: null,
  mock: false,
  timeout: 20000,
  headers: null,
  withCredentials: false,
  abortTime: 0,
  retry: false,
};
export function optimizeUrl(url) {
  if (typeof url === "string") {
    const newUrl = url
      .replace(/http(s)?[:]*[\/]*/g, "")
      .split("/")
      .filter(Boolean)
      .join("/")
      .replace(/[\/]{2,}/g, "/");
    if (url.startsWith("https")) {
      return "https://" + newUrl;
    }
    if (url.startsWith("http")) {
      return "http://" + newUrl;
    }
    if (url.startsWith("/")) {
      return "/" + newUrl;
    }
    return newUrl;
  }
  return "";
}
export function getOptimizeConfig(http, config = {}) {
  const globalConfig = http.getConfig() || {};
  const scopeConfig = {};
  for (const name in defaultConfig) {
    // mock只能单独开启，不能全局开启
    if (name === "mock") {
      const scopeMock = config[name];
      if (!scopeMock) {
        delete globalConfig[name];
      } else {
        const globalMock = globalConfig[name];
        scopeConfig[name] = {
          ...globalMock,
          ...scopeMock,
        };
      }
    } else if (config.hasOwnProperty(name)) {
      const scope = config[name];
      if (scope === null || scope === undefined) {
        delete config[name];
        delete globalConfig[name];
      } else if (name === "headers") {
        const headers = globalConfig[name];
        const globalHeaders = typeof headers === "function" ? headers(config) : headers;
        const scopeHeaders = typeof scope === "function" ? scope(config) : scope;
        scopeConfig[name] = omitParams({
          ...globalHeaders,
          ...scopeHeaders,
        });
      } else {
        scopeConfig[name] = scope;
      }
    }
  }
  return {
    ...defaultConfig,
    ...config,
    ...globalConfig,
    ...scopeConfig,
  };
}
export function omitParams(params) {
  if (isPlainObject(params)) {
    return omitBy(params, (val) => [null, undefined].includes(val));
  }
  return params;
}
/**
 * 统一处理SMS相关API响应的函数
 * @param {Object|number} responseOrCode - API响应对象 {code, msg, data} 或错误码
 * @param {Object} options - 配置选项
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 错误回调
 * @param {Object} options.countdownRef - 倒计时组件引用
 * @returns {boolean|string} 完整处理时返回是否成功，仅获取消息时返回错误消息
 */
export function handleSmsResponse(response, options = {}) {
  const { onSuccess, onError, countdownRef } = options;
  const { code, msg, data } = response || {};

  // 成功处理
  if (code === 200 || code === 0) {
    showToast(enTranslations.sendCodeMsgTip7);
    // 启动倒计时
    if (countdownRef?.value) {
      countdownRef.value.start();
    }
    // 执行成功回调
    if (typeof onSuccess === "function") {
      onSuccess(data);
    }
    return;
  }
  // 错误码映射表
  const responseTips = {
    103019: enTranslations.sendCodeMsgTip1, // 手机号码不存在
    103039: enTranslations.sendCodeMsgTip2, // 手机号格式不对
    103040: enTranslations.sendCodeMsgTip3, // 手机号被封
    103031: enTranslations.sendCodeMsgTip4, // 要先绑定手机号
    102031: enTranslations.sendCodeMsgTip5, // 手机号已经被绑定
    102041: enTranslations.sendCodeMsgTip6, // 短信发送失败
    102040: enTranslations.sendCodeMsgTip6, // 短信发送失败
    600: enTranslations.tipword23, // 特殊错误码
    200: enTranslations.sendCodeMsgTip7, // 成功提示
    0: enTranslations.sendCodeMsgTip7, // 成功提示
  };

  // 获取对应错误提示，没有则继续
  if (responseTips[code]) {
    showToast(responseTips[code]);
    // 执行错误回调
    if (typeof onError === "function") {
      onError(code, responseTips[code]);
    }
    return;
  }

  // 处理倒计时组件失败状态
  if (countdownRef?.value && typeof countdownRef.value.handleSendFailed === "function") {
    countdownRef.value.handleSendFailed();
  }

  return;
}

// 保持向后兼容性的别名函数
export function disposeSendMsgResponse(code) {
  return handleSmsResponse(code);
}
class CancelController {
  controller = new Map();
  abort = (key) => {
    if (key === undefined) {
      // 取消所有的请求
      for (let item of this.controller.values()) {
        item.ctrl.abort();
      }
      this.controller.clear();
    } else if (this.controller.has(key)) {
      this.controller.get(key).ctrl.abort();
      this.controller.delete(key);
    }
  };
  remove = (key) => {
    this.controller.delete(key);
  };
  generate = (key, abortTime) => {
    if (!this.controller.has(key)) {
      this.controller.set(key, { time: Date.now(), ctrl: new AbortController() });
    }
    const { time, ctrl } = this.controller.get(key);
    if (abortTime && Date.now() - time < abortTime) {
      // 在间隔abortTime毫秒内发送了相同的请求，且上次请求仍未完成，那就取消上次的请求
      this.abort(key);
      const newCtrl = new AbortController();
      this.controller.set(key, { time: Date.now(), ctrl: newCtrl });
      return newCtrl;
    }
    return ctrl;
  };
  has = (key) => {
    return this.controller.has(key);
  };
}
export const abortController = new CancelController();

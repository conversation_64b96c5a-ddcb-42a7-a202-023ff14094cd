<template>
  <div class="cloudflare-debug">
    <div class="debug-header">
      <h1>CloudFlare 验证调试页面</h1>
      <p>用于调试 CloudFlare 验证组件的弹窗空白问题</p>
    </div>

    <div class="debug-controls">
      <div class="control-group">
        <h3>配置信息</h3>
        <div class="config-display">
          <p><strong>当前模式:</strong> {{ currentConfig.debug_mode_main }}</p>
          <p><strong>页面主机:</strong> {{ currentConfig.ActivityPageHost[currentConfig.debug_mode_main] }}</p>
          <p><strong>站点密钥:</strong> {{ currentConfig.CloudflareVerify_SITE_KEY[currentConfig.debug_mode_main] }}</p>
        </div>
      </div>

      <div class="control-group">
        <h3>测试验证</h3>
        <div class="button-group">
          <button @click="testLoginSubmit" :disabled="loading">测试登录提交验证</button>
          <button @click="testGetCode" :disabled="loading">测试获取验证码</button>
          <button @click="testCustomScene" :disabled="loading">测试自定义场景</button>
        </div>
      </div>

      <div class="control-group">
        <h3>调试信息</h3>
        <div class="debug-info">
          <p><strong>验证状态:</strong> {{ verificationStatus }}</p>
          <p><strong>对话框显示:</strong> {{ showVerifyDialog ? '是' : '否' }}</p>
          <p><strong>验证 URL:</strong> {{ verifyUrl || '未生成' }}</p>
          <p><strong>可中断:</strong> {{ canInterrupt ? '是' : '否' }}</p>
        </div>
      </div>

      <div class="control-group">
        <h3>日志输出</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <button @click="clearLogs" class="clear-logs-btn">清空日志</button>
      </div>
    </div>

    <!-- CloudFlare 验证组件 -->
    <CloudflareVerification
      ref="verificationRef"
      :config="currentConfig"
      :advanced-config="advancedConfig"
      :enable-retry="true"
      :show-status-indicator="true"
      @success="onVerificationSuccess"
      @cancel="onVerificationCancel"
      @error="onVerificationError"
      @status-change="onStatusChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import {
  CloudflareVerification,
  useCloudflareVerification,
  CloudFlareScene,
  type CloudflareConfig,
  type AdvancedConfig,
  type VerificationStatus,
  type VerifyResult,
} from "@/components/CloudFlare";

// 配置
const currentConfig: CloudflareConfig = {
  ActivityPageHost: {
    PRE: "https://example.com/",
    DEV: "https://dev.example.com/",
    TEST: "https://test.example.com/",
  },
  CloudflareVerify_SITE_KEY: {
    PRE: "0x4AAAAAABr6liO_iAPr4Zx_",
    DEV: "0x4AAAAAABr6liO_iAPr4Zx_",
    TEST: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  debug_mode_main: "PRE",
};

const advancedConfig: AdvancedConfig = {
  enableStats: true,
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableLogging: true,
  logLevel: "info",
};

// 状态
const loading = ref(false);
const verificationRef = ref<InstanceType<typeof CloudflareVerification>>();

// 使用 composable 获取状态
const cloudflareVerification = useCloudflareVerification(currentConfig, advancedConfig);
const {
  showVerifyDialog,
  verifyUrl,
  verificationStatus,
  canInterrupt,
} = cloudflareVerification;

// 日志系统
interface LogItem {
  time: string;
  message: string;
  type: 'info' | 'success' | 'error' | 'warning';
}

const logs = ref<LogItem[]>([]);

const addLog = (message: string, type: LogItem['type'] = 'info') => {
  const time = new Date().toLocaleTimeString();
  logs.value.push({ time, message, type });
  console.log(`[${type.toUpperCase()}] ${message}`);
};

const clearLogs = () => {
  logs.value = [];
};

// 测试方法
const testLoginSubmit = async () => {
  loading.value = true;
  addLog("开始测试登录提交验证...", "info");
  
  try {
    const result = await verificationRef.value?.startCloudflareVerification(CloudFlareScene.LOGIN_SUBMIT);
    addLog(`登录提交验证完成: ${JSON.stringify(result)}`, "success");
  } catch (error: any) {
    addLog(`登录提交验证失败: ${error.message}`, "error");
  } finally {
    loading.value = false;
  }
};

const testGetCode = async () => {
  loading.value = true;
  addLog("开始测试获取验证码验证...", "info");
  
  try {
    const result = await verificationRef.value?.startCloudflareVerification(CloudFlareScene.LOGIN_PHONE_GET_CODE);
    addLog(`获取验证码验证完成: ${JSON.stringify(result)}`, "success");
  } catch (error: any) {
    addLog(`获取验证码验证失败: ${error.message}`, "error");
  } finally {
    loading.value = false;
  }
};

const testCustomScene = async () => {
  loading.value = true;
  addLog("开始测试自定义场景验证...", "info");
  
  try {
    const result = await verificationRef.value?.startCloudflareVerification(CloudFlareScene.BIND_PHONE_SUBMIT);
    addLog(`自定义场景验证完成: ${JSON.stringify(result)}`, "success");
  } catch (error: any) {
    addLog(`自定义场景验证失败: ${error.message}`, "error");
  } finally {
    loading.value = false;
  }
};

// 事件处理
const onVerificationSuccess = (result: VerifyResult) => {
  addLog(`验证成功: token=${result.token}, code=${result.code}`, "success");
};

const onVerificationCancel = () => {
  addLog("验证已取消", "warning");
};

const onVerificationError = (error: Error) => {
  addLog(`验证错误: ${error.message}`, "error");
};

const onStatusChange = (status: VerificationStatus) => {
  addLog(`验证状态变更: ${status}`, "info");
};

// 初始化日志
addLog("CloudFlare 验证调试页面已加载", "info");
addLog(`当前配置: ${JSON.stringify(currentConfig)}`, "info");
</script>

<style scoped>
.cloudflare-debug {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-header {
  text-align: center;
  margin-bottom: 30px;
}

.debug-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.debug-header p {
  color: #666;
  font-size: 16px;
}

.debug-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.control-group {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.control-group h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 18px;
}

.config-display p,
.debug-info p {
  margin: 8px 0;
  font-family: monospace;
  background: #fff;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.button-group button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button-group button:hover:not(:disabled) {
  background: #0056b3;
}

.button-group button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.info .log-message {
  color: #495057;
}

.log-item.success .log-message {
  color: #28a745;
}

.log-item.error .log-message {
  color: #dc3545;
}

.log-item.warning .log-message {
  color: #ffc107;
}

.clear-logs-btn {
  padding: 5px 10px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #fff;
  color: #495057;
  cursor: pointer;
  font-size: 12px;
}

.clear-logs-btn:hover {
  background: #f8f9fa;
}
</style>

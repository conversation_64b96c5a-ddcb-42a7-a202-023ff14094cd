/**
 * CloudFlare 验证函数调用 API
 * 提供简单易用的函数调用接口
 * 基于 cocos-ca-h5 项目重构优化
 */

import { ref, createApp } from "vue";
import type {
  CloudFlareScene,
  CloudflareConfig,
  CloudflareApiOptions,
  CloudflareApiResult,
  VerifyResult,
  AdvancedConfig,
} from "./types";
import { useCloudflareVerification } from "./useCloudflareVerification";
import CloudflareVerification from "./CloudflareVerification.vue";

// 全局实例管理
let globalVerificationInstance: ReturnType<typeof useCloudflareVerification> | null = null;
let globalConfig: CloudflareConfig | null = null;
let globalAdvancedConfig: AdvancedConfig | null = null;

/**
 * 初始化 CloudFlare 验证系统
 * @param config CloudFlare 配置
 * @param advancedConfig 高级配置
 */
export function initCloudflareVerification(
  config: CloudflareConfig,
  advancedConfig?: AdvancedConfig
): void {
  globalConfig = config;
  globalAdvancedConfig = advancedConfig;
  
  // 创建全局实例
  globalVerificationInstance = useCloudflareVerification(config, advancedConfig);
  
  console.log("CloudFlare 验证系统已初始化", { config, advancedConfig });
}

/**
 * 获取全局验证实例
 * @returns 验证实例
 */
function getVerificationInstance() {
  if (!globalVerificationInstance) {
    throw new Error("CloudFlare 验证系统未初始化，请先调用 initCloudflareVerification");
  }
  return globalVerificationInstance;
}

/**
 * 简单的 CloudFlare 验证函数
 * @param scene 验证场景
 * @param options 可选配置
 * @returns Promise<CloudflareApiResult>
 */
export async function verifyCloudflare(
  scene: CloudFlareScene,
  options: Partial<CloudflareApiOptions> = {}
): Promise<CloudflareApiResult> {
  const startTime = Date.now();
  
  try {
    const instance = getVerificationInstance();
    
    // 设置超时
    const timeout = options.timeout || 30000; // 默认30秒超时
    const timeoutPromise = new Promise<VerifyResult>((_, reject) => {
      setTimeout(() => reject(new Error("验证超时")), timeout);
    });
    
    // 执行验证
    const verifyPromise = instance.getVerifyToken(scene);
    
    // 竞争执行
    const result = await Promise.race([verifyPromise, timeoutPromise]);
    
    const duration = Date.now() - startTime;
    
    return {
      ...result,
      scene,
      timestamp: startTime,
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    
    console.error("CloudFlare 验证失败:", error);
    
    return {
      token: "",
      code: 2,
      scene,
      timestamp: startTime,
      duration,
    };
  }
}

/**
 * 带重试的 CloudFlare 验证函数
 * @param scene 验证场景
 * @param options 配置选项
 * @returns Promise<CloudflareApiResult>
 */
export async function verifyCloudflareWithRetry(
  scene: CloudFlareScene,
  options: Partial<CloudflareApiOptions> = {}
): Promise<CloudflareApiResult> {
  const maxRetries = options.retries || 3;
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      options.onProgress?.(`尝试验证 ${attempt}/${maxRetries}`);
      
      const result = await verifyCloudflare(scene, options);
      
      if (result.code === 0) {
        return result;
      }
      
      lastError = new Error(`验证失败，代码: ${result.code}`);
      
      // 如果不是最后一次尝试，等待一段时间再重试
      if (attempt < maxRetries) {
        const delay = 1000 * attempt; // 递增延迟
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    } catch (error) {
      lastError = error as Error;
      
      if (attempt < maxRetries) {
        const delay = 1000 * attempt;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  // 所有重试都失败了
  throw lastError || new Error("验证失败");
}

/**
 * 快速验证函数（使用默认配置）
 * @param scene 验证场景
 * @returns Promise<string> 返回 token 或抛出错误
 */
export async function quickVerify(scene: CloudFlareScene): Promise<string> {
  const result = await verifyCloudflare(scene);
  
  if (result.code === 0 && result.token) {
    return result.token;
  }
  
  throw new Error(`验证失败，代码: ${result.code}`);
}

/**
 * 消费最后的验证 token
 * @returns token 数据或 null
 */
export function consumeVerificationToken() {
  const instance = getVerificationInstance();
  return instance.consumeToken();
}

/**
 * 获取验证统计信息
 * @returns 统计信息
 */
export function getVerificationStats() {
  const instance = getVerificationInstance();
  return instance.getStats();
}

/**
 * 清除验证统计信息
 */
export function clearVerificationStats() {
  const instance = getVerificationInstance();
  instance.clearStats();
}

/**
 * 重置验证状态
 */
export function resetVerificationState() {
  const instance = getVerificationInstance();
  instance.resetVerification();
}

/**
 * 检查验证系统是否已初始化
 * @returns 是否已初始化
 */
export function isVerificationInitialized(): boolean {
  return globalVerificationInstance !== null;
}

/**
 * 获取当前配置
 * @returns 当前配置
 */
export function getCurrentConfig() {
  return {
    config: globalConfig,
    advancedConfig: globalAdvancedConfig,
  };
}

/**
 * 创建验证组件实例（用于手动挂载）
 * @param container 容器元素
 * @param props 组件属性
 * @returns Vue 应用实例
 */
export function createVerificationComponent(
  container: HTMLElement,
  props: Record<string, any> = {}
) {
  const app = createApp(CloudflareVerification, {
    config: globalConfig,
    ...props,
  });
  
  app.mount(container);
  
  return app;
}

// 导出常用的验证场景常量
export const VerificationScenes = {
  LOGIN: "SCENE_LOGIN" as CloudFlareScene,
  GET_CODE: "SCENE_GET_CODE" as CloudFlareScene,
  FORGET_PASSWORD: "SCENE_FORGET_PASSWORD" as CloudFlareScene,
  WITHDRAW: "SCENE_WITHDRAW" as CloudFlareScene,
  KYC: "SCENE_SUB_KYC_INFO" as CloudFlareScene,
  BIND_PHONE: "SCENE_BIND_PT_PHONE" as CloudFlareScene,
  CHANGE_PHONE: "SCENE_CHANGE_PT_PHONE" as CloudFlareScene,
} as const;

// 默认导出主要函数
export default {
  init: initCloudflareVerification,
  verify: verifyCloudflare,
  verifyWithRetry: verifyCloudflareWithRetry,
  quickVerify,
  consumeToken: consumeVerificationToken,
  getStats: getVerificationStats,
  clearStats: clearVerificationStats,
  reset: resetVerificationState,
  isInitialized: isVerificationInitialized,
  getConfig: getCurrentConfig,
  createComponent: createVerificationComponent,
  scenes: VerificationScenes,
};

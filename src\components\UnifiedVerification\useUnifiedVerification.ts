/**
 * 统一验证组件 - 支持GeetestMgr和CloudFlare两种验证模式
 * 用于统一管理项目中的验证逻辑
 */

import { ref, computed } from "vue";
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useCloudflareVerification } from "@/components/CloudFlare/useCloudflareVerification";
import { CloudFlareScene } from "@/components/CloudFlare/types";
import type { VerifyResult } from "@/components/CloudFlare/types";

// 验证模式枚举
export enum VerificationMode {
  GEETEST = "geetest",
  CLOUDFLARE = "cloudflare",
  BOTH = "both", // 两种验证都执行
}

// 验证场景类型
export interface VerificationScene {
  // Geetest相关
  geetestType?: string;
  geetestDevice?: string;

  // CloudFlare相关
  cloudflareGetCodeScene?: CloudFlareScene;
  cloudflareSubmitScene?: CloudFlareScene;
}

// 验证结果统一格式
export interface UnifiedVerifyResult {
  success: boolean;
  geetestResult?: any;
  cloudflareResult?: VerifyResult;
  error?: string;
}

// 验证配置
export interface UnifiedVerificationConfig {
  mode: VerificationMode;
  scene: VerificationScene;
  enableLogging?: boolean;
}

/**
 * 统一验证 Composable
 */
export function useUnifiedVerification(config: UnifiedVerificationConfig) {
  const isVerifying = ref(false);
  const cloudflareVerification = useCloudflareVerification();

  /**
   * 执行Geetest验证
   */
  const executeGeetestVerification = async (geetestType: string, phone?: string): Promise<any> => {
    return new Promise((resolve) => {
      GeetestMgr.instance.geetest_device(
        geetestType,
        (result) => {
          if (result) {
            let ret = {};
            if (Object.getPrototypeOf(result) !== Boolean.prototype) {
              ret = result;
            }
            resolve(ret);
          } else {
            resolve(null);
          }
        },
        phone
      );
    });
  };

  /**
   * 执行CloudFlare验证
   */
  const executeCloudflareVerification = async (scene: CloudFlareScene): Promise<VerifyResult> => {
    return await cloudflareVerification.getVerifyToken(scene);
  };

  /**
   * 统一验证方法 - 发送验证码场景
   */
  const verifyForSendCode = async (phone?: string): Promise<UnifiedVerifyResult> => {
    if (config.enableLogging) {
      console.log("🔐 开始发送验证码验证", { mode: config.mode, scene: config.scene });
    }

    isVerifying.value = true;

    try {
      const result: UnifiedVerifyResult = { success: false };

      switch (config.mode) {
        case VerificationMode.GEETEST:
          if (config.scene.geetestType) {
            result.geetestResult = await executeGeetestVerification(
              config.scene.geetestType,
              phone
            );
            result.success = !!result.geetestResult;
          }
          break;

        case VerificationMode.CLOUDFLARE:
          if (config.scene.cloudflareGetCodeScene) {
            result.cloudflareResult = await executeCloudflareVerification(
              config.scene.cloudflareGetCodeScene
            );
            result.success = result.cloudflareResult.code === 0;
          }
          break;

        case VerificationMode.BOTH:
          // 先执行Geetest验证
          if (config.scene.geetestType) {
            result.geetestResult = await executeGeetestVerification(
              config.scene.geetestType,
              phone
            );
          }

          // 再执行CloudFlare验证
          if (config.scene.cloudflareGetCodeScene) {
            result.cloudflareResult = await executeCloudflareVerification(
              config.scene.cloudflareGetCodeScene
            );
          }

          // 两种验证都成功才算成功
          result.success = !!result.geetestResult && result.cloudflareResult?.code === 0;
          break;

        default:
          result.error = "未知的验证模式";
          break;
      }

      if (config.enableLogging) {
        console.log("🔐 发送验证码验证结果", result);
      }

      return result;
    } catch (error) {
      if (config.enableLogging) {
        console.error("🔐 发送验证码验证失败", error);
      }
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
      };
    } finally {
      isVerifying.value = false;
    }
  };

  /**
   * 统一验证方法 - 提交验证码场景
   */
  const verifyForSubmit = async (): Promise<UnifiedVerifyResult> => {
    if (config.enableLogging) {
      console.log("🔐 开始提交验证码验证", { mode: config.mode, scene: config.scene });
    }

    isVerifying.value = true;

    try {
      const result: UnifiedVerifyResult = { success: false };

      switch (config.mode) {
        case VerificationMode.GEETEST:
          if (config.scene.geetestDevice) {
            result.geetestResult = await executeGeetestVerification(config.scene.geetestDevice);
            result.success = !!result.geetestResult;
          }
          break;

        case VerificationMode.CLOUDFLARE:
          if (config.scene.cloudflareSubmitScene) {
            result.cloudflareResult = await executeCloudflareVerification(
              config.scene.cloudflareSubmitScene
            );
            result.success = result.cloudflareResult.code === 0;
          }
          break;

        case VerificationMode.BOTH:
          // 先执行Geetest验证
          if (config.scene.geetestDevice) {
            result.geetestResult = await executeGeetestVerification(config.scene.geetestDevice);
          }

          // 再执行CloudFlare验证
          if (config.scene.cloudflareSubmitScene) {
            result.cloudflareResult = await executeCloudflareVerification(
              config.scene.cloudflareSubmitScene
            );
          }

          // 两种验证都成功才算成功
          result.success = !!result.geetestResult && result.cloudflareResult?.code === 0;
          break;

        default:
          result.error = "未知的验证模式";
          break;
      }

      if (config.enableLogging) {
        console.log("🔐 提交验证码验证结果", result);
      }

      return result;
    } catch (error) {
      if (config.enableLogging) {
        console.error("🔐 提交验证码验证失败", error);
      }
      return {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
      };
    } finally {
      isVerifying.value = false;
    }
  };

  /**
   * 构建API参数 - 合并两种验证的结果
   */
  const buildApiParams = (
    verifyResult: UnifiedVerifyResult,
    baseParams: Record<string, any> = {}
  ): Record<string, any> => {
    const params = { ...baseParams };

    // 添加Geetest参数
    if (verifyResult.geetestResult) {
      params.geetest_guard = verifyResult.geetestResult.geetest_guard || "";
      params.userInfo = verifyResult.geetestResult.userInfo || "";
      params.geetest_captcha = verifyResult.geetestResult.geetest_captcha || "";
      params.buds = verifyResult.geetestResult.buds || "64";
    }

    // 添加CloudFlare参数
    if (verifyResult.cloudflareResult) {
      params["cf-token"] = verifyResult.cloudflareResult.token || "";
      // 可以根据需要添加更多CloudFlare相关参数
    }

    return params;
  };

  return {
    // 状态
    isVerifying: computed(() => isVerifying.value),

    // CloudFlare相关状态（透传）
    showVerifyDialog: cloudflareVerification.showVerifyDialog,
    verifyUrl: cloudflareVerification.verifyUrl,
    verificationStatus: cloudflareVerification.verificationStatus,
    canInterrupt: cloudflareVerification.canInterrupt,

    // 方法
    verifyForSendCode,
    verifyForSubmit,
    buildApiParams,

    // CloudFlare相关方法（透传）
    closeVerify: cloudflareVerification.closeVerify,
    onVerifySuccess: cloudflareVerification.onVerifySuccess,
    onVerifyFailed: cloudflareVerification.onVerifyFailed,
    onVerifyTimeout: cloudflareVerification.onVerifyTimeout,
    onMessageHandler: cloudflareVerification.onMessageHandler,
  };
}

/**
 * 预定义的验证场景配置
 */
export const VERIFICATION_SCENES = {
  // 绑定手机号
  BIND_PHONE: {
    geetestType: GEETEST_TYPE.bind_pt_phone_code,
    geetestDevice: GEETEST_TYPE.bind_pt_phone,
    cloudflareGetCodeScene: CloudFlareScene.BIND_PHONE_GET_CODE,
    cloudflareSubmitScene: CloudFlareScene.BIND_PHONE_SUBMIT,
  },

  // 修改手机号
  CHANGE_PHONE: {
    geetestType: GEETEST_TYPE.change_pt_phone_code,
    geetestDevice: GEETEST_TYPE.change_pt_phone,
    cloudflareGetCodeScene: CloudFlareScene.MODIFY_PHONE_GET_CODE,
    cloudflareSubmitScene: CloudFlareScene.MODIFY_PHONE_SUBMIT,
  },

  // 绑定提现账户
  BIND_WITHDRAW_ACCOUNT: {
    geetestType: GEETEST_TYPE.bind_withdraw_account_code,
    geetestDevice: GEETEST_TYPE.bind_withdraw_account,
    cloudflareGetCodeScene: CloudFlareScene.BIND_WITHDRAWAL_ACCOUNT_GET_CODE,
    cloudflareSubmitScene: CloudFlareScene.BIND_WITHDRAWAL_ACCOUNT_SUBMIT,
  },

  // 忘记密码
  FORGET_PASSWORD: {
    geetestType: GEETEST_TYPE.forget_password_code,
    geetestDevice: GEETEST_TYPE.forget_password,
    cloudflareGetCodeScene: CloudFlareScene.FORGET_PW_GET_CODE,
    cloudflareSubmitScene: CloudFlareScene.FORGET_PW_SUBMIT,
  },

  // 首次设置登录密码
  FIRST_LOGIN_PASSWORD: {
    geetestType: GEETEST_TYPE.first_password,
    geetestDevice: GEETEST_TYPE.first_password,
    cloudflareGetCodeScene: CloudFlareScene.FIRST_SET_LOGIN_PW,
    cloudflareSubmitScene: CloudFlareScene.FIRST_SET_LOGIN_PW,
  },

  // 首次设置支付密码
  FIRST_PAY_PASSWORD: {
    geetestType: GEETEST_TYPE.first_pay_password,
    geetestDevice: GEETEST_TYPE.first_pay_password,
    cloudflareGetCodeScene: CloudFlareScene.FIRST_SET_PAY_PW,
    cloudflareSubmitScene: CloudFlareScene.FIRST_SET_PAY_PW,
  },

  // 提现
  WITHDRAW: {
    geetestType: GEETEST_TYPE.withdraw,
    geetestDevice: GEETEST_TYPE.withdraw,
    cloudflareSubmitScene: CloudFlareScene.WITHDRAWAL_SUBMIT,
  },
} as const;

/**
 * CloudFlare 验证组件类型定义
 * 基于 cocos-ca-h5 项目重构优化
 */

// CloudFlare 验证场景枚举（与后端保持同步配置）
export enum CloudFlareScene {
  /** 无 */
  NONE = "",
  /** 手机号注册登录-获取验证码 */
  LOGIN_PHONE_GET_CODE = "SCENE_GET_CODE",
  /** 登录-提交 */
  LOGIN_SUBMIT = "SCENE_LOGIN",
  /** 忘记密码-获取验证码 */
  FORGET_PW_GET_CODE = "SCENE_FORGET_PW_GET_CODE",
  /** 忘记密码-提交 */
  FORGET_PW_SUBMIT = "SCENE_FORGET_PASSWORD",
  /** 首次设定登录密码 */
  FIRST_SET_LOGIN_PW = "SCENE_FIRST_PASSWORD",
  /** 首次设定支付密码 */
  FIRST_SET_PAY_PW = "SCENE_FIRST_PAY_PASSWORD",
  /** 修改登录密码-获取验证码 */
  MODIFY_LOGIN_PW_GET_CODE = "SCENE_MODIFY_LOGIN_PW_GET_CODE",
  /** 修改登录密码-提交 */
  MODIFY_LOGIN_PW_SUBMIT = "SCENE_CHANGE_PASSWORD",
  /** 修改支付密码-获取验证码 */
  MODIFY_PAY_PW_GET_CODE = "xxx",
  /** 修改支付密码-提交 */
  MODIFY_PAY_PW_SUBMIT = "SCENE_CHANGE_PAY_PASSWORD",
  /** 绑定提款账号-获取验证码 */
  BIND_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 绑定提款账号-提交 */
  BIND_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_BIND_WITHDRAW_ACCOUNT",
  /** 修改提款账号-获取验证码 */
  MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 修改提款账号-提交 */
  MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_CHANGE_WITHDRAW_ACCOUNT",
  /** 提现-提交订单 */
  WITHDRAWAL_SUBMIT = "SCENE_WITHDRAW",
  /** 绑定手机号-获取验证码 */
  BIND_PHONE_GET_CODE = "xxx",
  /** 绑定手机号-提交 */
  BIND_PHONE_SUBMIT = "SCENE_BIND_PT_PHONE",
  /** 修改手机号-获取验证码 */
  MODIFY_PHONE_GET_CODE = "SCENE_MODIFY_PHONE_GET_CODE",
  /** 修改手机号-提交 */
  MODIFY_PHONE_SUBMIT = "SCENE_CHANGE_PT_PHONE",
  /** KYC 提交 */
  KYC_SUBMIT = "SCENE_SUB_KYC_INFO",
}

// 手机验证类型枚举
export enum VerifyType {
  SetPhoneNumber = 0,
  ForgetPassword = 1,
  ChangePhoneNumber = 2,
  AddWithdrawAccount = 3,
  ChangeWithdrawAccount = 4,
}

// 短信类型枚举
export enum SmsType {
  BIND_PHONE = 1,
  UPDATE_LOGIN_PASSWORD = 2,
  UPDATE_PHONE = 3,
  BIND_WITHDRAW_ACCOUNT = 4,
  UPDATE_WITHDRAW_ACCOUNT = 5,
}

// CloudFlare 验证结果
export interface VerifyResult {
  token: string;
  code: number; // 0: 成功, 1: 操作锁定, 2: 验证失败
}

// CloudFlare 配置信息
export interface CloudflareInfo {
  mode: "managed" | "invisible";
  sitekey: string;
  scene?: string;
}

// CloudFlare 配置映射表
export interface CloudflareInfoMap {
  [scene: string]: CloudflareInfo;
}

// CloudFlare 配置
export interface CloudflareConfig {
  ActivityPageHost: Record<string, string>;
  CloudflareVerify_SITE_KEY: Record<string, string>;
  debug_mode_main: string;
}

// 操作锁配置
export interface OperationLockConfig {
  duration: number; // 锁定时长（毫秒）
  enabled: boolean; // 是否启用操作锁
}

// 用户数据
export interface UserData {
  id: string;
  phone?: string;
  email?: string;
  username?: string;
  [key: string]: any;
}

// API 响应
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  success?: boolean;
}

// 手机验证参数
export interface PhoneVerificationParams {
  phone: string;
  telephoneCode: string;
  type: SmsType;
  buds?: string;
  "cf-token"?: string;
  "cf-scene"?: CloudFlareScene;
}

// 短信验证参数
export interface SmsVerifyParams {
  phone: string;
  telephoneCode: string;
  code: string;
  type: SmsType;
}

// 绑定手机参数
export interface BindPhoneParams {
  phone: string;
  verifyCode: string;
  telephoneCode: string;
  buds?: string;
  "cf-token"?: string;
  "cf-scene"?: CloudFlareScene;
}

// 修改手机参数
export interface ChangePhoneParams {
  old_phone?: string;
  phone: string;
  verifyCode: string;
  telephoneCode: string;
  buds?: string;
  "cf-token"?: string;
  "cf-scene"?: CloudFlareScene;
}

// CloudFlare 控制器接口
export interface CloudflareController {
  getVerifyToken(
    scene: CloudFlareScene,
    callback?: (result: VerifyResult) => void
  ): Promise<VerifyResult>;
  consumeToken(): { scene: CloudFlareScene; token: string } | null;
  lastScene: CloudFlareScene;
  lastToken: string;
  isOperationLocked: boolean;
  setOperationLock(locked: boolean, duration?: number): void;
}

// 消息处理器类型
export type MessageHandler = (event: MessageEvent) => void;

// 验证回调类型
export type VerifyCallback = (result: VerifyResult) => void;

// 平台信息
export interface PlatformInfo {
  isNative: boolean;
  os: "android" | "ios" | "web" | "windows" | "macos" | "linux";
  userAgent: string;
}

// 验证步骤
export type VerificationStep = "phone" | "code";

// 验证状态
export interface VerificationState {
  visible: boolean;
  currentStep: VerificationStep;
  phoneNumber: string;
  verificationCode: string;
  phoneError: boolean;
  phoneErrorMessage: string;
  codeError: boolean;
  codeErrorMessage: string;
  isSending: boolean;
  isSubmitting: boolean;
  isCountingDown: boolean;
  countdown: number;
}

// 组件事件
export interface ComponentEvents {
  success: (data: any) => void;
  cancel: () => void;
  error: (error: Error) => void;
}

// 本地存储键
export enum StorageKeys {
  USER_DATA = "userdata",
  TOKEN = "token",
  PHONE = "PHONE",
  COUNTDOWN_ENDTIME = "COUNTDOWN_ENDTIME",
}

// 错误类型
export interface ErrorInfo {
  message: string;
  code?: number;
  stack?: string;
  timestamp: number;
}

// 验证配置
export interface VerificationConfig {
  enableCloudflare: boolean;
  countdownDuration: number;
  maxRetries: number;
  autoClose: boolean;
}

// 场景配置映射
export interface SceneConfigMap {
  [key: string]: {
    getCodeScene?: CloudFlareScene;
    submitScene: CloudFlareScene;
    smsType: SmsType;
    title: string;
    description: string;
  };
}

// 默认场景配置
export const DEFAULT_SCENE_CONFIG: SceneConfigMap = {
  [VerifyType.SetPhoneNumber]: {
    getCodeScene: CloudFlareScene.BIND_PHONE_GET_CODE,
    submitScene: CloudFlareScene.BIND_PHONE_SUBMIT,
    smsType: SmsType.BIND_PHONE,
    title: "绑定手机号",
    description: "请输入您的手机号码进行绑定",
  },
  [VerifyType.ChangePhoneNumber]: {
    getCodeScene: CloudFlareScene.MODIFY_PHONE_GET_CODE,
    submitScene: CloudFlareScene.MODIFY_PHONE_SUBMIT,
    smsType: SmsType.UPDATE_PHONE,
    title: "修改手机号",
    description: "请输入新的手机号码",
  },
  [VerifyType.ForgetPassword]: {
    getCodeScene: CloudFlareScene.FORGET_PW_GET_CODE,
    submitScene: CloudFlareScene.FORGET_PW_SUBMIT,
    smsType: SmsType.UPDATE_LOGIN_PASSWORD,
    title: "忘记密码",
    description: "请输入您的手机号码以重置密码",
  },
  [VerifyType.AddWithdrawAccount]: {
    getCodeScene: CloudFlareScene.BIND_WITHDRAWAL_ACCOUNT_GET_CODE,
    submitScene: CloudFlareScene.BIND_WITHDRAWAL_ACCOUNT_SUBMIT,
    smsType: SmsType.BIND_WITHDRAW_ACCOUNT,
    title: "添加提现账户",
    description: "请验证您的手机号码",
  },
  [VerifyType.ChangeWithdrawAccount]: {
    getCodeScene: CloudFlareScene.MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE,
    submitScene: CloudFlareScene.MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT,
    smsType: SmsType.UPDATE_WITHDRAW_ACCOUNT,
    title: "修改提现账户",
    description: "请验证您的手机号码",
  },
};

// 验证结果类型
export interface PhoneVerificationResult {
  success: boolean;
  data?: any;
  error?: string;
  verifyType: VerifyType;
}

// 组件 Props 类型
export interface CloudflareVerificationProps {
  verifyType: VerifyType;
  initialPhone?: string;
  config?: CloudflareConfig;
  enableAutoClose?: boolean;
  countdownDuration?: number;
}

// 组件 Emits 类型
export interface CloudflareVerificationEmits {
  (e: "success", result: PhoneVerificationResult): void;
  (e: "cancel"): void;
  (e: "error", error: Error): void;
}

// 函数调用API选项
export interface CloudflareApiOptions {
  scene: CloudFlareScene;
  config?: CloudflareConfig;
  timeout?: number; // 超时时间（毫秒）
  retries?: number; // 重试次数
  autoClose?: boolean; // 失败时自动关闭
  onProgress?: (step: string) => void; // 进度回调
}

// 函数调用API结果
export interface CloudflareApiResult extends VerifyResult {
  scene: CloudFlareScene;
  timestamp: number;
  duration: number; // 验证耗时（毫秒）
}

// 验证状态枚举
export enum VerificationStatus {
  IDLE = "idle",
  LOADING = "loading",
  VERIFYING = "verifying",
  SUCCESS = "success",
  FAILED = "failed",
  CANCELLED = "cancelled",
  TIMEOUT = "timeout",
}

// 验证统计信息
export interface VerificationStats {
  totalAttempts: number;
  successCount: number;
  failureCount: number;
  averageDuration: number;
  lastAttemptTime: number;
}

// 高级配置选项
export interface AdvancedConfig {
  enableStats: boolean; // 是否启用统计
  enableRetry: boolean; // 是否启用重试
  maxRetries: number; // 最大重试次数
  retryDelay: number; // 重试延迟（毫秒）
  enableLogging: boolean; // 是否启用日志
  logLevel: "debug" | "info" | "warn" | "error"; // 日志级别
}

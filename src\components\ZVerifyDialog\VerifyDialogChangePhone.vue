<!-- 绑定、变更手机号弹窗 -->
<template>
  <ZActionSheet
    v-model="visible"
    :title="getTypeInfo?.title"
    confirmText="Confirm"
    :showCancelButton="false"
    :onConfirm="handleConfirm"
    :onCancel="handleCancel"
  >
    <div class="phone-input" v-if="dialogStep === 1">
      <PhoneNumberInput
        ref="phoneInputRef"
        v-model="phone"
        label="Your phone number (9xx xxxx xxx)"
        :required="true"
        :validateOnBlur="false"
      />
    </div>
    <div class="dialog-content" v-if="dialogStep === 2">
      <div class="send-code-tip">
        A text message with a 6-digit code was just sent to
        <b>{{ formattedUserPhone }}</b>
      </div>
      <div class="phone-input-code">
        <VerificationCodeInput
          ref="verificationCodeRef"
          class="verificationCode"
          v-model="verificationCode"
        />
        <CodeCountdownButton ref="countdownButtonRef" @click="checkPhoneIsRegister" />
      </div>
    </div>
  </ZActionSheet>

  <!-- 统一验证对话框 -->
  <UnifiedVerificationDialog
    v-model:show-dialog="unifiedVerification.showVerifyDialog"
    :verify-url="unifiedVerification.verifyUrl"
    :verification-status="unifiedVerification.verificationStatus"
    :can-interrupt="unifiedVerification.canInterrupt"
    title="安全验证"
    @cancel="unifiedVerification.closeVerify"
    @retry="handleVerificationRetry"
    @iframe-load="handleIframeLoad"
  />
</template>

<script setup lang="ts">
import { sendCodeMsg, changePhone, bindPhone } from "@/api/setPhoneNumber";
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, computed, watch } from "vue";
import { showToast } from "vant";
import { storeToRefs } from "pinia";
import { GlobalEnum } from "@/utils/config/GlobalEnum";
import enTranslations from "@/utils/I18n/en.json";
import { handleSmsResponse } from "@/utils/http/util";

import { PN_VERIFY_TYPE } from "./types";
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import CodeCountdownButton from "@/components/CodeCountdownButton.vue";
import PhoneNumberInput from "@/components/PhoneNumberInput/index.vue";
import VerificationCodeInput from "@/components/VerificationCodeInput/index.vue";
// 导入统一验证组件
import {
  useUnifiedVerification,
  UnifiedVerificationDialog,
  VerificationMode,
  VERIFICATION_SCENES,
} from "@/components/UnifiedVerification";

const globalStore = useGlobalStore();
const { userInfo } = storeToRefs(globalStore);
const countdownButtonRef = ref();
const phoneInputRef = ref();
const verificationCodeRef = ref();

const props = defineProps({
  // 显示弹窗
  showNextDialog: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 设置手机号0、更改手机号2
  verifyType: {
    type: Number,
    default: 2,
    required: true,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => {},
    required: false,
  },
});

// 统一验证配置
const verificationConfig = computed(() => {
  const scene =
    props.verifyType === PN_VERIFY_TYPE.SetPhoneNumber
      ? VERIFICATION_SCENES.BIND_PHONE
      : VERIFICATION_SCENES.CHANGE_PHONE;

  return {
    mode: VerificationMode.BOTH, // 同时使用两种验证
    scene,
    enableLogging: true,
  };
});

// 初始化统一验证
const unifiedVerification = useUnifiedVerification(verificationConfig.value);

// 当前流程进度
const dialogStep = ref(1);
// 弹窗是否显示
const visible = ref(props.showNextDialog);
// 手机号
const phone = ref("");
// 验证码
const verificationCode = ref("");
// 是否已发送验证码
const hasSentCode = ref(false);

const resetData = () => {
  dialogStep.value = 1;
  phone.value = "";
  verificationCode.value = "";
  hasSentCode.value = false;
  verificationCodeRef.value?.clear();
};

watch(
  () => props.showNextDialog,
  (val) => {
    visible.value = val;
    if (!val) {
      resetData();
    }
  }
);

// 监听 visible 变化，同步到父组件
watch(
  () => visible.value,
  (val) => {
    if (val !== props.showNextDialog) {
      emit("update:showNextDialog", val);
    }
  }
);

const formattedUserPhone = computed(() => {
  const newPhone = phone.value || "";
  return newPhone.slice(0, 2) + "****" + newPhone.slice(6, 10);
});

const getTypeInfo = computed(() => {
  if (props.verifyType === PN_VERIFY_TYPE.SetPhoneNumber) {
    return {
      title: "Set Phone Number",
      gtype: GEETEST_TYPE.bind_pt_phone_code,
      msgType: GlobalEnum.SMS_TYPE.BIND_PHONE,
      geetestDevice: GEETEST_TYPE.bind_pt_phone,
    };
  } else if (props.verifyType === PN_VERIFY_TYPE.ChangePhoneNumber) {
    return {
      title: "Change Phone Number",
      gtype: GEETEST_TYPE.change_pt_phone_code,
      msgType: GlobalEnum.SMS_TYPE.UPDATE_PHONE,
      geetestDevice: GEETEST_TYPE.change_pt_phone,
    };
  }
});

//发送验证码之前先进行统一验证
const checkPhoneIsRegister = async () => {
  try {
    // 使用统一验证组件
    const verifyResult = await unifiedVerification.verifyForSendCode(phone.value);

    if (verifyResult.success) {
      // 构建API参数，合并两种验证的结果
      const baseParams = {
        phone: phone.value,
        telephoneCode: "+63",
        type: getTypeInfo.value?.msgType,
      };

      const apiParams = unifiedVerification.buildApiParams(verifyResult, baseParams);
      await checkPhoneIsRegister_true(apiParams);
    } else {
      showToast(verifyResult.error || "验证失败，请重试");
    }
  } catch (error) {
    console.error("验证失败:", error);
    showToast("验证失败，请重试");
  }
};
//发送验证码
const checkPhoneIsRegister_true = async (ret) => {
  let params = {
    phone: phone.value,
    telephoneCode: "+63",
    geetest_guard: ret?.geetest_guard || "",
    userInfo: ret?.userInfo || "",
    geetest_captcha: ret?.geetest_captcha || "",
    buds: ret?.buds || "64",
    type: getTypeInfo.value?.msgType,
  };
  const response = await sendCodeMsg(params);

  handleSmsResponse(response, {
    countdownRef: countdownButtonRef,
    onSuccess: () => {
      hasSentCode.value = true;
    },
    onError: (code: any, message: any) => {
      console.error("发送验证码失败:", code, message);
    },
  });
};

const emit = defineEmits(["update:showNextDialog", "complete"]);

const handleCancel = () => {
  emit("update:showNextDialog", false);
};

const handleConfirm = async () => {
  if (dialogStep.value === 1) {
    if (phoneInputRef.value?.isValid()) {
      dialogStep.value = 2;
    } else {
      phoneInputRef.value?.validate();
    }
  } else if (dialogStep.value === 2) {
    if (verificationCodeRef.value?.isValid()) {
      try {
        // 使用统一验证组件进行提交验证
        const verifyResult = await unifiedVerification.verifyForSubmit();

        if (verifyResult.success) {
          // 构建API参数，合并两种验证的结果
          const baseParams = {
            phone: phone.value,
            verifyCode: verificationCode.value,
            telephoneCode: "+63",
          };

          const apiParams = unifiedVerification.buildApiParams(verifyResult, baseParams);

          if (props.verifyType === PN_VERIFY_TYPE.SetPhoneNumber) {
            await handleBindPhone(apiParams);
          } else {
            await handleChangePhone(apiParams);
          }
        } else {
          showToast(verifyResult.error || "验证失败，请重试");
        }
      } catch (error) {
        console.error("提交验证失败:", error);
        showToast("验证失败，请重试");
      }
    } else {
      verificationCodeRef.value?.validate();
    }
  }
};
/**
 * 构建通用的API参数
 * @param ret - 验证码返回数据
 * @returns 通用参数对象
 */
const buildCommonParams = (ret: any) => ({
  phone: phone.value,
  verifyCode: verificationCode.value,
  telephoneCode: "+63",
  geetest_guard: ret?.geetest_guard || "",
  userInfo: ret?.userInfo || "",
  geetest_captcha: ret?.geetest_captcha || "",
  buds: ret?.buds || "64",
});

/**
 * 处理API响应的通用逻辑
 * @param code - 响应状态码
 * @param msg - 响应消息
 */
const handleApiResponse = (code: number, msg: string) => {
  if (code === 200) {
    showToast("Phone number set successfully.");
    handleCancel();
    globalStore.updateUserInfo({ phone: phone.value });
    props.succCallBack && props.succCallBack();
    emit("complete"); // 通知父组件完成
  } else {
    msg && showToast(msg);
  }
};

/**
 * 通用的手机号操作处理函数
 * @param apiFunction - API 函数 (changePhone 或 bindPhone)
 * @param specificParams - 特定的API参数
 * @param ret - 验证码返回数据
 */
const handlePhoneOperation = async (apiFunction: Function, specificParams: any, ret: any) => {
  const commonParams = buildCommonParams(ret);
  const { code, msg } = await apiFunction({
    ...specificParams,
    ...commonParams,
  });

  handleApiResponse(code, msg);
};

// 更新手机号
const handleChangePhone = async (apiParams: any) => {
  const specificParams = {
    old_phone: userInfo.value.phone,
  };

  const finalParams = { ...specificParams, ...apiParams };

  try {
    const response = await changePhone(finalParams);
    const { code, msg } = response.data || response;
    handleApiResponse(code, msg);
  } catch (error) {
    console.error("更新手机号失败:", error);
    showToast("更新手机号失败，请重试");
  }
};

// 绑定手机号
const handleBindPhone = async (apiParams: any) => {
  const specificParams = {
    appPackageName: ALL_APP_SOURCE_CONFIG.appPackageName,
    deviceId: ALL_APP_SOURCE_CONFIG.deviceId,
    appVersion: ALL_APP_SOURCE_CONFIG.appVersion,
  };

  const finalParams = { ...specificParams, ...apiParams };

  try {
    const response = await bindPhone(finalParams);
    const { code, msg } = response.data || response;
    handleApiResponse(code, msg);
  } catch (error) {
    console.error("绑定手机号失败:", error);
    showToast("绑定手机号失败，请重试");
  }
};

// 处理验证重试
const handleVerificationRetry = () => {
  // 可以根据当前步骤重新触发验证
  if (dialogStep.value === 1) {
    // 重新发送验证码
    checkPhoneIsRegister();
  } else if (dialogStep.value === 2) {
    // 重新提交验证码
    handleConfirm();
  }
};

// 处理iframe加载
const handleIframeLoad = () => {
  console.log("CloudFlare验证iframe已加载");
};
</script>

<style scoped lang="scss">
.dialog-content {
  padding-top: 12px;

  // 验证码步骤样式
  .send-code-tip {
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;

    /* 171.429% */
  }

  .phone-input-code {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    .verificationCode {
      flex: 1;
      height: 40px;
      border: 1px solid #eee;
      border-radius: 20px;
      padding: 0 12px;
      outline: none;
      background-color: #f4f7fd;
    }
  }
}
</style>

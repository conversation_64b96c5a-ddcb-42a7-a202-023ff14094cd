<!--
  测试修复后的 VerifyDialogChangePhone 组件
-->
<template>
  <div class="test-container">
    <h2>测试修复后的验证组件</h2>

    <van-button type="primary" @click="showBindPhoneDialog = true" block>
      测试绑定手机号
    </van-button>

    <van-button type="success" @click="showChangePhoneDialog = true" block style="margin-top: 12px">
      测试修改手机号
    </van-button>

    <!-- 绑定手机号对话框 -->
    <VerifyDialogChangePhone
      v-model:show-next-dialog="showBindPhoneDialog"
      :verify-type="0"
      :succ-call-back="handleBindSuccess"
      @complete="handleComplete"
    />

    <!-- 修改手机号对话框 -->
    <VerifyDialogChangePhone
      v-model:show-next-dialog="showChangePhoneDialog"
      :verify-type="2"
      :succ-call-back="handleChangeSuccess"
      @complete="handleComplete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { showToast } from "vant";
import VerifyDialogChangePhone from "./VerifyDialogChangePhone.vue";

const showBindPhoneDialog = ref(false);
const showChangePhoneDialog = ref(false);

const handleBindSuccess = () => {
  showToast("绑定手机号成功！");
  console.log("绑定手机号成功回调");
};

const handleChangeSuccess = () => {
  showToast("修改手机号成功！");
  console.log("修改手机号成功回调");
};

const handleComplete = () => {
  console.log("验证完成");
};

// 测试统一验证组件是否正常工作
console.log("测试组件已加载，可以点击按钮测试验证功能");
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  max-width: 400px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
  }
}
</style>

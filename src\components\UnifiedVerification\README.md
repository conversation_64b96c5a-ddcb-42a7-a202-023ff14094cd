# 统一验证组件

这个组件提供了一个统一的验证接口，支持 GeetestMgr 和 CloudFlare 两种验证模式，可以单独使用或同时使用。

## 功能特性

- 🔄 支持三种验证模式：GeetestMgr、CloudFlare、两者同时
- 🎯 预定义常用验证场景配置
- 🔧 灵活的参数构建和合并
- 📱 支持 CloudFlare 验证的 UI 对话框
- 📊 可选的验证统计和日志记录

## 基本用法

### 1. 导入组件

```typescript
import { 
  useUnifiedVerification, 
  UnifiedVerificationDialog,
  VerificationMode, 
  VERIFICATION_SCENES 
} from "@/components/UnifiedVerification";
```

### 2. 配置验证

```typescript
// 配置验证参数
const verificationConfig = {
  mode: VerificationMode.BOTH, // 同时使用两种验证
  scene: VERIFICATION_SCENES.BIND_PHONE, // 使用预定义场景
  enableLogging: true, // 启用日志
};

// 初始化验证
const unifiedVerification = useUnifiedVerification(verificationConfig);
```

### 3. 执行验证

```typescript
// 发送验证码时的验证
const sendCodeVerification = async () => {
  const result = await unifiedVerification.verifyForSendCode(phoneNumber);
  
  if (result.success) {
    // 构建API参数
    const apiParams = unifiedVerification.buildApiParams(result, {
      phone: phoneNumber,
      telephoneCode: "+63",
      type: smsType,
    });
    
    // 调用发送验证码API
    await sendCodeAPI(apiParams);
  } else {
    showToast(result.error || "验证失败");
  }
};

// 提交验证码时的验证
const submitVerification = async () => {
  const result = await unifiedVerification.verifyForSubmit();
  
  if (result.success) {
    // 构建API参数
    const apiParams = unifiedVerification.buildApiParams(result, {
      phone: phoneNumber,
      verifyCode: code,
      telephoneCode: "+63",
    });
    
    // 调用提交API
    await submitAPI(apiParams);
  } else {
    showToast(result.error || "验证失败");
  }
};
```

### 4. 添加 CloudFlare 验证对话框

```vue
<template>
  <!-- 你的其他内容 -->
  
  <!-- CloudFlare 验证对话框 -->
  <UnifiedVerificationDialog
    v-model:show-dialog="unifiedVerification.showVerifyDialog.value"
    :verify-url="unifiedVerification.verifyUrl.value"
    :verification-status="unifiedVerification.verificationStatus.value"
    :can-interrupt="unifiedVerification.canInterrupt.value"
    title="安全验证"
    @cancel="unifiedVerification.closeVerify"
    @retry="handleRetry"
    @iframe-load="handleIframeLoad"
  />
</template>
```

## 验证模式

### VerificationMode.GEETEST
只使用 GeetestMgr 验证

### VerificationMode.CLOUDFLARE
只使用 CloudFlare 验证

### VerificationMode.BOTH
同时使用两种验证，只有都成功才算成功

## 预定义场景

```typescript
VERIFICATION_SCENES = {
  BIND_PHONE,           // 绑定手机号
  CHANGE_PHONE,         // 修改手机号
  BIND_WITHDRAW_ACCOUNT, // 绑定提现账户
  FORGET_PASSWORD,      // 忘记密码
  FIRST_LOGIN_PASSWORD, // 首次设置登录密码
  FIRST_PAY_PASSWORD,   // 首次设置支付密码
  WITHDRAW,             // 提现
}
```

## 自定义场景

```typescript
const customScene = {
  geetestType: "custom_geetest_type",
  geetestDevice: "custom_geetest_device",
  cloudflareGetCodeScene: CloudFlareScene.CUSTOM_GET_CODE,
  cloudflareSubmitScene: CloudFlareScene.CUSTOM_SUBMIT,
};

const config = {
  mode: VerificationMode.BOTH,
  scene: customScene,
  enableLogging: true,
};
```

## API 参数构建

`buildApiParams` 方法会自动合并两种验证的结果：

```typescript
// Geetest 参数
{
  geetest_guard: "...",
  userInfo: "...",
  geetest_captcha: "...",
  buds: "64",
}

// CloudFlare 参数
{
  "cf-token": "...",
}

// 合并后的参数包含所有验证信息
```

## 错误处理

```typescript
const result = await unifiedVerification.verifyForSendCode();

if (!result.success) {
  console.error("验证失败:", result.error);
  showToast(result.error || "验证失败，请重试");
}
```

## 注意事项

1. 确保在使用前已正确初始化 GeetestMgr 和 CloudFlare 验证
2. CloudFlare 验证需要网络连接和正确的配置
3. 在 `BOTH` 模式下，任一验证失败都会导致整体失败
4. 建议在生产环境中启用日志记录以便调试

## 迁移指南

### 从 GeetestMgr 迁移

```typescript
// 原来的代码
GeetestMgr.instance.geetest_device(GEETEST_TYPE.bind_pt_phone_code, (result) => {
  if (result) {
    // 处理结果
  }
});

// 迁移后的代码
const config = {
  mode: VerificationMode.GEETEST,
  scene: VERIFICATION_SCENES.BIND_PHONE,
};
const verification = useUnifiedVerification(config);
const result = await verification.verifyForSendCode();
if (result.success) {
  // 处理结果
}
```

这样可以逐步迁移现有代码，同时为将来添加 CloudFlare 验证做好准备。

<template>
  <div class="cloudflare-example">
    <h2>CloudFlare 验证组件示例</h2>

    <!-- 函数调用模式示例 -->
    <div class="example-section">
      <h3>1. 函数调用模式（推荐）</h3>
      <div class="button-group">
        <button @click="quickVerifyExample" :disabled="loading">快速验证</button>
        <button @click="verifyWithRetryExample" :disabled="loading">带重试验证</button>
        <button @click="customVerifyExample" :disabled="loading">自定义验证</button>
      </div>

      <div v-if="apiResult" class="result-box">
        <h4>API 调用结果:</h4>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>

    <!-- 组件模式示例 -->
    <div class="example-section">
      <h3>2. 组件模式</h3>
      <div class="button-group">
        <button @click="startComponentVerification" :disabled="loading">开始组件验证</button>
        <button @click="resetComponent">重置组件</button>
      </div>

      <CloudflareVerification
        ref="verificationRef"
        :config="cloudflareConfig"
        :advanced-config="advancedConfig"
        :enable-retry="true"
        :show-status-indicator="true"
        @success="onComponentSuccess"
        @cancel="onComponentCancel"
        @error="onComponentError"
        @status-change="onStatusChange"
      />
    </div>

    <!-- 状态显示 -->
    <div class="status-section">
      <h3>验证状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <label>当前状态:</label>
          <span :class="['status-badge', currentStatus]">{{ currentStatus }}</span>
        </div>
        <div class="status-item">
          <label>加载中:</label>
          <span :class="['status-badge', loading ? 'loading' : 'idle']">
            {{ loading ? "是" : "否" }}
          </span>
        </div>
      </div>
    </div>

    <!-- 日志显示 -->
    <div class="log-section">
      <h3>操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="clear-btn">清除日志</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import {
  CloudflareAPI,
  CloudflareVerification,
  useCloudflareVerification,
  CloudFlareScene,
  type CloudflareConfig,
  type AdvancedConfig,
  type VerificationStatus,
} from "./index";

// 配置
const cloudflareConfig: CloudflareConfig = {
  ActivityPageHost: {
    PRE: "https://example.com/",
    DEV: "https://dev.example.com/",
    TEST: "https://test.example.com/",
  },
  CloudflareVerify_SITE_KEY: {
    PRE: "0x4AAAAAABr6liO_iAPr4Zx_",
    DEV: "0x4AAAAAABr6liO_iAPr4Zx_",
    TEST: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  debug_mode_main: "PRE",
};

const advancedConfig: AdvancedConfig = {
  enableStats: true,
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableLogging: true,
  logLevel: "info",
};

// 状态
const loading = ref(false);
const currentStatus = ref<VerificationStatus>("idle" as VerificationStatus);
const apiResult = ref<any>(null);

// 组件引用
const verificationRef = ref();

// 日志系统
interface LogItem {
  time: string;
  message: string;
  type: "info" | "success" | "error" | "warning";
}

const logs = reactive<LogItem[]>([]);

const addLog = (message: string, type: LogItem["type"] = "info") => {
  logs.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type,
  });

  // 限制日志数量
  if (logs.length > 50) {
    logs.splice(50);
  }
};

// 初始化 API
if (!CloudflareAPI.isInitialized()) {
  CloudflareAPI.init(cloudflareConfig, advancedConfig);
  addLog("CloudFlare API 已初始化", "success");
}

// 函数调用模式示例
const quickVerifyExample = async () => {
  loading.value = true;
  addLog("开始快速验证...", "info");

  try {
    const token = await CloudflareAPI.quickVerify(CloudFlareScene.LOGIN_SUBMIT);
    apiResult.value = { token, success: true };
    addLog(`快速验证成功: ${token}`, "success");
  } catch (error: any) {
    apiResult.value = { error: error.message, success: false };
    addLog(`快速验证失败: ${error.message}`, "error");
  } finally {
    loading.value = false;
  }
};

const verifyWithRetryExample = async () => {
  loading.value = true;
  addLog("开始带重试验证...", "info");

  try {
    const result = await CloudflareAPI.verifyWithRetry(CloudFlareScene.LOGIN_SUBMIT, {
      retries: 3,
      timeout: 30000,
      onProgress: (step) => {
        addLog(`验证进度: ${step}`, "info");
      },
    });

    apiResult.value = result;
    addLog(`带重试验证成功: ${result.token}`, "success");
  } catch (error: any) {
    apiResult.value = { error: error.message, success: false };
    addLog(`带重试验证失败: ${error.message}`, "error");
  } finally {
    loading.value = false;
  }
};

const customVerifyExample = async () => {
  loading.value = true;
  addLog("开始自定义验证...", "info");

  try {
    const result = await CloudflareAPI.verify(CloudFlareScene.LOGIN_PHONE_GET_CODE, {
      timeout: 20000,
      autoClose: true,
    });

    apiResult.value = result;
    addLog(`自定义验证完成: code=${result.code}`, result.code === 0 ? "success" : "error");
  } catch (error: any) {
    apiResult.value = { error: error.message, success: false };
    addLog(`自定义验证失败: ${error.message}`, "error");
  } finally {
    loading.value = false;
  }
};

// 组件模式示例
const startComponentVerification = () => {
  addLog("开始组件验证...", "info");
  verificationRef.value?.startCloudflareVerification(CloudFlareScene.LOGIN_SUBMIT);
};

const resetComponent = () => {
  addLog("重置组件状态", "info");
  verificationRef.value?.resetVerification();
  currentStatus.value = "idle" as VerificationStatus;
};

const onComponentSuccess = (result: any) => {
  addLog(`组件验证成功: ${result.token}`, "success");
  apiResult.value = result;
};

const onComponentCancel = () => {
  addLog("组件验证已取消", "warning");
};

const onComponentError = (error: Error) => {
  addLog(`组件验证错误: ${error.message}`, "error");
};

const onStatusChange = (status: VerificationStatus) => {
  currentStatus.value = status;
  addLog(`状态变化: ${status}`, "info");
};

const clearLogs = () => {
  logs.splice(0);
};
</script>

<style scoped>
.cloudflare-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;
}

.button-group {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background: #007bff;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

button:hover:not(:disabled) {
  background: #0056b3;
}

button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.clear-btn {
  background: #dc3545;
  margin-top: 10px;
}

.clear-btn:hover {
  background: #c82333;
}

.result-box {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 16px;
  margin-top: 16px;
}

.result-box pre {
  margin: 0;
  font-size: 12px;
  overflow-x: auto;
}

.status-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #fff;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.idle {
  background: #6c757d;
  color: white;
}
.status-badge.loading {
  background: #007bff;
  color: white;
}
.status-badge.verifying {
  background: #ffc107;
  color: black;
}
.status-badge.success {
  background: #28a745;
  color: white;
}
.status-badge.failed {
  background: #dc3545;
  color: white;
}
.status-badge.locked {
  background: #dc3545;
  color: white;
}
.status-badge.unlocked {
  background: #28a745;
  color: white;
}

.log-section {
  padding: 20px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #fff;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #f8f9fa;
  padding: 12px;
  font-family: "Courier New", monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  padding: 2px 0;
}

.log-time {
  color: #6c757d;
  margin-right: 12px;
  min-width: 80px;
}

.log-item.info .log-message {
  color: #007bff;
}
.log-item.success .log-message {
  color: #28a745;
}
.log-item.error .log-message {
  color: #dc3545;
}
.log-item.warning .log-message {
  color: #ffc107;
}

h2,
h3,
h4 {
  color: #333;
  margin-top: 0;
}

h3 {
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}
</style>

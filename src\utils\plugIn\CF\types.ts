/**
 * Vue3 Phone Verification Types
 * 转换自 Cocos Creator TypeScript 代码
 */

// Cloudflare 验证场景枚举
export enum CloudFlareScene {
  /** 无 */
  NONE = '',
  /** 手机号注册登录-获取验证码 */
  LOGIN_PHONE_GET_CODE = 'SCENE_GET_CODE',
  /** 登录-提交 */
  LOGIN_SUBMIT = 'SCENE_LOGIN',
  /** 忘记密码-获取验证码 */
  FORGET_PW_GET_CODE = 'SCENE_FORGET_PW_GET_CODE',
  /** 忘记密码-提交 */
  FORGET_PW_SUBMIT = 'SCENE_FORGET_PASSWORD',
  /** 首次设定登录密码 */
  FIRST_SET_LOGIN_PW = 'SCENE_FIRST_PASSWORD',
  /** 首次设定支付密码 */
  FIRST_SET_PAY_PW = 'SCENE_FIRST_PAY_PASSWORD',
  /** 修改登录密码-获取验证码 */
  MODIFY_LOGIN_PW_GET_CODE = 'SCENE_MODIFY_LOGIN_PW_GET_CODE',
  /** 修改登录密码-提交 */
  MODIFY_LOGIN_PW_SUBMIT = 'SCENE_CHANGE_PASSWORD',
  /** 修改支付密码-获取验证码 */
  MODIFY_PAY_PW_GET_CODE = 'xxx',
  /** 修改支付密码-提交 */
  MODIFY_PAY_PW_SUBMIT = 'SCENE_CHANGE_PAY_PASSWORD',
  /** 绑定提款账号-获取验证码 */
  BIND_WITHDRAWAL_ACCOUNT_GET_CODE = 'xxx',
  /** 绑定提款账号-提交 */
  BIND_WITHDRAWAL_ACCOUNT_SUBMIT = 'SCENE_BIND_WITHDRAW_ACCOUNT',
  /** 修改提款账号-获取验证码 */
  MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE = 'xxx',
  /** 修改提款账号-提交 */
  MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = 'SCENE_CHANGE_WITHDRAW_ACCOUNT',
  /** 提现-提交订单 */
  WITHDRAWAL_SUBMIT = 'SCENE_WITHDRAW',
  /** 绑定手机号-获取验证码 */
  BIND_PHONE_GET_CODE = 'xxx',
  /** 绑定手机号-提交 */
  BIND_PHONE_SUBMIT = 'SCENE_BIND_PT_PHONE',
  /** 修改手机号-获取验证码 */
  MODIFY_PHONE_GET_CODE = 'SCENE_MODIFY_PHONE_GET_CODE',
  /** 修改手机号-提交 */
  MODIFY_PHONE_SUBMIT = 'SCENE_CHANGE_PT_PHONE',
  /** KYC 提交 */
  KYC_SUBMIT = 'SCENE_SUB_KYC_INFO'
}

// 手机号验证类型枚举
export enum VerifyType {
  SetPhoneNumber = 0,
  ForgetPassword = 1,
  ChangePhoneNumber = 2,
  AddWithdrawAccount = 3,
  ChangeWithdrawAccount = 4
}

// 短信类型枚举
export enum SmsType {
  BIND_PHONE = 1,
  UPDATE_LOGIN_PASSWORD = 2,
  UPDATE_PHONE = 3,
  BIND_WITHDRAW_ACCOUNT = 4,
  UPDATE_WITHDRAW_ACCOUNT = 5
}

// Cloudflare 验证信息接口
export interface CloudflareInfo {
  mode: 'managed' | 'invisible'
  sitekey: string
  scene?: string
}

// 验证结果接口
export interface VerifyResult {
  token: string
  code: number
}

// 手机号验证参数接口
export interface PhoneVerificationParams {
  phone: string
  telephoneCode: string
  type: SmsType | VerifyType
  buds?: string
  userInfo?: string
  geetest_guard?: string
  geetest_captcha?: string
  'cf-token'?: string
  'cf-scene'?: CloudFlareScene
}

// 绑定手机号参数接口
export interface BindPhoneParams {
  token?: string
  appPackageName?: string
  deviceId?: string
  appVersion?: string
  phone: string
  verifyCode: string
  telephoneCode: string
  buds?: string
  userInfo?: string
  geetest_guard?: string
  geetest_captcha?: string
  'cf-token'?: string
  'cf-scene'?: CloudFlareScene
}

// 修改手机号参数接口
export interface ChangePhoneParams {
  token?: string
  old_phone: string
  phone: string
  verifyCode: string
  telephoneCode: string
  buds?: string
  userInfo?: string
  geetest_guard?: string
  geetest_captcha?: string
  'cf-token'?: string
  'cf-scene'?: CloudFlareScene
}

// 短信验证参数接口
export interface SmsVerifyParams {
  phone: string
  telephoneCode: string
  code: string
  type: SmsType
}

// 用户数据接口
export interface UserData {
  user_id: string
  phone?: string
  [key: string]: any
}

// 应用配置接口
export interface AppConfig {
  ActivityPageHost: Record<string, string>
  CloudflareVerify_SITE_KEY: Record<string, string>
  debug_mode_main: string
  channel?: string
}

// 全局存储键枚举
export enum GlobalStorageKey {
  PHONE = 'PHONE',
  COUNTDOWN_ENDTIME = 'COUNTDOWN_ENDTIME',
  TOKEN = 'TOKEN'
}

// API 响应接口
export interface ApiResponse<T = any> {
  code: number
  msg?: string
  data?: T
}

// 错误信息接口
export interface ErrorInfo {
  code: string | number
  message: string
}

// 组件事件接口
export interface ComponentEvents {
  onSuccess?: (data: any) => void
  onCancel?: () => void
  onError?: (error: ErrorInfo) => void
}

// Cloudflare 控制器接口
export interface CloudflareController {
  getVerifyToken(scene: CloudFlareScene): Promise<VerifyResult>
  consumeToken(): { scene: CloudFlareScene; token: string } | null
  lastScene: CloudFlareScene
  lastToken: string
}

// 全局状态管理接口
export interface GlobalStore {
  userdata: UserData | null
  token: string
  loginVerifyType: number
  showTip(message: string): void
  sendSms(params: PhoneVerificationParams): Promise<ApiResponse>
  verifySmsCode(params: SmsVerifyParams): Promise<ApiResponse>
  bindPhone(params: BindPhoneParams): Promise<ApiResponse>
  changePhone(params: ChangePhoneParams): Promise<ApiResponse>
  getStorageData(key: string, defaultValue?: any): any
  setStorageData(key: string, value: any): void
  needScreenUp(): boolean
  getCloudflareController(): CloudflareController
}

// 国际化接口
export interface I18nInstance {
  t(key: string, params?: Record<string, any>): string
}

// 手机号验证组件属性接口
export interface PhoneVerificationProps {
  verifyType: VerifyType
  initialPhone?: string
  onSuccess?: (data: any) => void
  onCancel?: () => void
}

// Cloudflare 组件属性接口
export interface CloudflareProps {
  config?: AppConfig
}

// 倒计时状态接口
export interface CountdownState {
  isActive: boolean
  remaining: number
  endTime: number
}

// 表单验证规则接口
export interface ValidationRule {
  required?: boolean
  pattern?: RegExp
  message: string
  validator?: (value: string) => boolean
}

// 表单字段接口
export interface FormField {
  value: string
  error: boolean
  errorMessage: string
  rules: ValidationRule[]
}

// 组件状态接口
export interface ComponentState {
  visible: boolean
  loading: boolean
  step: 'phone' | 'code'
  countdown: CountdownState
}

// 网络请求选项接口
export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  timeout?: number
  retries?: number
}

// 工具函数类型
export type PhoneFormatter = (phone: string) => string
export type PhoneValidator = (phone: string) => boolean
export type ErrorHandler = (error: Error) => void
export type SuccessHandler<T = any> = (data: T) => void

// 常量定义
export const PHONE_REGEX = /^(09|\+639)\d{9}$/
export const VERIFICATION_CODE_LENGTH = 6
export const DEFAULT_COUNTDOWN_DURATION = 60
export const DEFAULT_OPERATION_LOCK_DURATION = 3000

// 标题映射
export const VERIFY_TYPE_TITLES: Record<VerifyType, string> = {
  [VerifyType.SetPhoneNumber]: 'Set Phone Number',
  [VerifyType.ForgetPassword]: 'Change Login Password',
  [VerifyType.ChangePhoneNumber]: 'Change Phone Number',
  [VerifyType.AddWithdrawAccount]: 'Add Fund Account',
  [VerifyType.ChangeWithdrawAccount]: 'Change Fund Account'
}

// SMS 类型映射
export const SMS_TYPE_MAPPING: Record<VerifyType, SmsType> = {
  [VerifyType.SetPhoneNumber]: SmsType.BIND_PHONE,
  [VerifyType.ForgetPassword]: SmsType.UPDATE_LOGIN_PASSWORD,
  [VerifyType.ChangePhoneNumber]: SmsType.UPDATE_PHONE,
  [VerifyType.AddWithdrawAccount]: SmsType.BIND_WITHDRAW_ACCOUNT,
  [VerifyType.ChangeWithdrawAccount]: SmsType.UPDATE_WITHDRAW_ACCOUNT
}

// Cloudflare 场景映射
export const CLOUDFLARE_SCENE_MAPPING: Record<VerifyType, { getCode: CloudFlareScene; submit: CloudFlareScene }> = {
  [VerifyType.SetPhoneNumber]: {
    getCode: CloudFlareScene.BIND_PHONE_GET_CODE,
    submit: CloudFlareScene.BIND_PHONE_SUBMIT
  },
  [VerifyType.ForgetPassword]: {
    getCode: CloudFlareScene.FORGET_PW_GET_CODE,
    submit: CloudFlareScene.FORGET_PW_SUBMIT
  },
  [VerifyType.ChangePhoneNumber]: {
    getCode: CloudFlareScene.MODIFY_PHONE_GET_CODE,
    submit: CloudFlareScene.MODIFY_PHONE_SUBMIT
  },
  [VerifyType.AddWithdrawAccount]: {
    getCode: CloudFlareScene.BIND_WITHDRAWAL_ACCOUNT_GET_CODE,
    submit: CloudFlareScene.BIND_WITHDRAWAL_ACCOUNT_SUBMIT
  },
  [VerifyType.ChangeWithdrawAccount]: {
    getCode: CloudFlareScene.MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE,
    submit: CloudFlareScene.MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT
  }
}

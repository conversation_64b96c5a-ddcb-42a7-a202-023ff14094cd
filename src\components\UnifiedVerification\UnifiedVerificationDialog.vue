<!--
  统一验证对话框组件
  支持CloudFlare验证的UI展示
-->
<template>
  <!-- CloudFlare验证对话框 -->
  <van-dialog
    v-model:show="showVerifyDialog"
    :title="dialogTitle"
    :show-cancel-button="canInterrupt"
    :cancel-button-text="'取消'"
    :confirm-button-text="''"
    :show-confirm-button="false"
    :close-on-click-overlay="canInterrupt"
    class="cloudflare-verify-dialog"
    @cancel="handleCancel"
  >
    <div class="verify-content">
      <div v-if="verificationStatus === 'loading'" class="loading-content">
        <van-loading type="spinner" size="24px" />
        <p>正在初始化验证...</p>
      </div>
      
      <div v-else-if="verificationStatus === 'verifying'" class="iframe-container">
        <iframe
          :src="verifyUrl"
          frameborder="0"
          class="verify-iframe"
          @load="onIframeLoad"
        ></iframe>
        
        <div v-if="canInterrupt" class="interrupt-hint">
          <p>验证时间较长？</p>
          <van-button size="small" type="primary" @click="handleCancel">
            取消验证
          </van-button>
        </div>
      </div>
      
      <div v-else-if="verificationStatus === 'success'" class="success-content">
        <van-icon name="success" size="48px" color="#07c160" />
        <p>验证成功</p>
      </div>
      
      <div v-else-if="verificationStatus === 'failed'" class="failed-content">
        <van-icon name="close" size="48px" color="#ee0a24" />
        <p>验证失败，请重试</p>
        <van-button size="small" type="primary" @click="handleRetry">
          重新验证
        </van-button>
      </div>
      
      <div v-else-if="verificationStatus === 'timeout'" class="timeout-content">
        <van-icon name="clock-o" size="48px" color="#ff976a" />
        <p>验证超时，请重试</p>
        <van-button size="small" type="primary" @click="handleRetry">
          重新验证
        </van-button>
      </div>
    </div>
  </van-dialog>
</template>

<script setup lang="ts">
import { computed, watch } from "vue";
import { VerificationStatus } from "@/components/CloudFlare/types";

interface Props {
  showDialog: boolean;
  verifyUrl: string;
  verificationStatus: VerificationStatus;
  canInterrupt: boolean;
  title?: string;
}

interface Emits {
  (e: "update:showDialog", value: boolean): void;
  (e: "cancel"): void;
  (e: "retry"): void;
  (e: "iframe-load"): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: "安全验证",
});

const emit = defineEmits<Emits>();

// 计算属性
const showVerifyDialog = computed({
  get: () => props.showDialog,
  set: (value) => emit("update:showDialog", value),
});

const dialogTitle = computed(() => {
  switch (props.verificationStatus) {
    case VerificationStatus.LOADING:
      return "正在加载验证...";
    case VerificationStatus.VERIFYING:
      return props.title;
    case VerificationStatus.SUCCESS:
      return "验证成功";
    case VerificationStatus.FAILED:
      return "验证失败";
    case VerificationStatus.TIMEOUT:
      return "验证超时";
    default:
      return props.title;
  }
});

// 事件处理
const handleCancel = () => {
  emit("cancel");
};

const handleRetry = () => {
  emit("retry");
};

const onIframeLoad = () => {
  emit("iframe-load");
};

// 监听状态变化，自动关闭成功状态的对话框
watch(
  () => props.verificationStatus,
  (newStatus) => {
    if (newStatus === VerificationStatus.SUCCESS) {
      setTimeout(() => {
        showVerifyDialog.value = false;
      }, 1500);
    }
  }
);
</script>

<style scoped lang="scss">
.cloudflare-verify-dialog {
  :deep(.van-dialog__content) {
    padding: 0;
    min-height: 300px;
  }
}

.verify-content {
  width: 100%;
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .loading-content,
  .success-content,
  .failed-content,
  .timeout-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    
    p {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }
  
  .iframe-container {
    width: 100%;
    height: 100%;
    position: relative;
    
    .verify-iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
    
    .interrupt-hint {
      position: absolute;
      bottom: 16px;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      background: rgba(255, 255, 255, 0.9);
      padding: 8px 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      p {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: #666;
      }
    }
  }
}
</style>

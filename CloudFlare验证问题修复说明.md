# CloudFlare 验证组件弹窗空白问题修复说明

## 问题描述

CloudFlare 验证组件 `CloudflareVerification.vue` 在使用时出现弹窗空白的问题，iframe 内容无法正常显示。

## 问题分析

经过代码分析，发现以下几个可能的问题：

### 1. 场景配置不完整
原始的 `cfInfoMap` 只包含了两个基础场景：
```typescript
cfInfoMap: {
  SCENE_LOGIN: {
    mode: "managed",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  SCENE_GET_CODE: {
    mode: "invisible", 
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
}
```

但实际使用中可能会调用其他场景，如 `CloudFlareScene.LOGIN_SUBMIT`、`CloudFlareScene.LOGIN_PHONE_GET_CODE` 等，这些场景没有对应的配置。

### 2. URL 构建问题
当场景配置不存在时，`verifyUrl` 可能会构建失败，导致 iframe 指向无效的 URL。

### 3. 配置参数问题
`pageHost` 和 `sitekey` 可能为 `undefined`，导致构建的 URL 无效。

## 修复方案

### 1. 完善场景配置映射

在 `useCloudflareVerification.ts` 中完善了 `cfInfoMap` 配置，添加了所有可能的验证场景：

```typescript
cloudflareState.cfInfoMap = {
  // 登录相关
  [CloudFlareScene.LOGIN_SUBMIT]: {
    mode: "managed",
    sitekey: currentConfig.CloudflareVerify_SITE_KEY?.[currentConfig.debug_mode_main] || "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  [CloudFlareScene.LOGIN_PHONE_GET_CODE]: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
  // 忘记密码
  [CloudFlareScene.FORGET_PW_GET_CODE]: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
  [CloudFlareScene.FORGET_PW_SUBMIT]: {
    mode: "managed",
    sitekey: currentConfig.CloudflareVerify_SITE_KEY?.[currentConfig.debug_mode_main] || "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // ... 其他场景
};
```

### 2. 增加 URL 验证

在构建验证 URL 后增加了有效性检查：

```typescript
// 检查 URL 是否有效
if (!verifyUrl.value || verifyUrl.value === 'undefined/turnstile.html?siteKey=undefined&appearance=always&isNative=0&os=web') {
  setOperationLock(false);
  verificationStatus.value = VerificationStatus.FAILED;
  const errorMsg = `验证 URL 构建失败: pageHost=${pageHost}, sitekey=${cfInfo.sitekey}`;
  showNotification(errorMsg);
  ErrorHandler.logError(new Error(errorMsg), "getVerifyToken");
  
  const result = { token: "", code: 1 };
  updateStats(false, Date.now() - startTime);
  resolve(result);
  callback?.(result);
  return;
}
```

### 3. 增强调试信息

在 `CloudflareVerification.vue` 和 `useCloudflareVerification.ts` 中增加了更详细的日志输出：

```typescript
if (currentAdvancedConfig.enableLogging) {
  console.log(`CloudFlare 验证开始: ${scene}`, {
    cfInfo,
    verifyUrl: verifyUrl.value,
    platform,
    pageHost,
    sitekey: cfInfo.sitekey,
  });
}
```

### 4. 创建调试页面

创建了 `CloudFlareDebug.vue` 调试页面，用于：
- 显示当前配置信息
- 测试不同验证场景
- 实时查看验证状态和 URL
- 输出详细的调试日志

## 使用方法

### 1. 访问调试页面
访问 `/cloudflare-debug` 路径可以打开调试页面，实时查看验证过程。

### 2. 检查配置
确保传入的 `CloudflareConfig` 包含正确的配置：

```typescript
const config: CloudflareConfig = {
  ActivityPageHost: {
    PRE: "https://your-domain.com/",  // 确保这个 URL 是有效的
    DEV: "https://dev.your-domain.com/",
    TEST: "https://test.your-domain.com/",
  },
  CloudflareVerify_SITE_KEY: {
    PRE: "your-actual-site-key",  // 确保这是有效的 CloudFlare 站点密钥
    DEV: "your-dev-site-key",
    TEST: "your-test-site-key",
  },
  debug_mode_main: "PRE",  // 或 "DEV", "TEST"
};
```

### 3. 启用调试日志
设置 `advancedConfig.enableLogging = true` 来查看详细的调试信息。

## 常见问题排查

### 1. 弹窗显示但内容空白
- 检查 `verifyUrl` 是否正确生成
- 确认 `pageHost` 配置的 URL 是否可访问
- 检查 `sitekey` 是否有效

### 2. 验证配置未找到错误
- 确认调用的场景在 `cfInfoMap` 中有对应配置
- 检查场景枚举值是否正确

### 3. URL 构建失败
- 检查 `ActivityPageHost` 配置是否包含当前模式的 URL
- 确认 `debug_mode_main` 设置正确

### 4. 网络问题
- 确认目标 URL 可以正常访问
- 检查是否有跨域限制
- 确认 CloudFlare 服务是否正常

## 测试建议

1. 使用调试页面测试各种验证场景
2. 检查浏览器控制台的错误信息
3. 确认网络请求是否成功
4. 验证 CloudFlare 配置的有效性

## 注意事项

1. 确保 `turnstile.html` 页面在指定的 `pageHost` 下存在且可访问
2. CloudFlare 站点密钥必须与域名匹配
3. 某些场景可能需要特定的权限或配置
4. 在生产环境中使用前，请充分测试所有验证场景

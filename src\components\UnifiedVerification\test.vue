<!--
  统一验证组件测试页面
  用于测试各种验证场景和模式
-->
<template>
  <div class="test-page">
    <h2>统一验证组件测试</h2>
    
    <!-- 验证模式选择 -->
    <div class="section">
      <h3>验证模式</h3>
      <van-radio-group v-model="selectedMode">
        <van-radio :name="VerificationMode.GEETEST">仅 GeetestMgr</van-radio>
        <van-radio :name="VerificationMode.CLOUDFLARE">仅 CloudFlare</van-radio>
        <van-radio :name="VerificationMode.BOTH">两种验证</van-radio>
      </van-radio-group>
    </div>
    
    <!-- 验证场景选择 -->
    <div class="section">
      <h3>验证场景</h3>
      <van-radio-group v-model="selectedScene">
        <van-radio name="BIND_PHONE">绑定手机号</van-radio>
        <van-radio name="CHANGE_PHONE">修改手机号</van-radio>
        <van-radio name="BIND_WITHDRAW_ACCOUNT">绑定提现账户</van-radio>
        <van-radio name="FORGET_PASSWORD">忘记密码</van-radio>
      </van-radio-group>
    </div>
    
    <!-- 手机号输入 -->
    <div class="section">
      <h3>手机号</h3>
      <van-field
        v-model="phoneNumber"
        label="手机号"
        placeholder="请输入手机号"
        :rules="[{ required: true, message: '请输入手机号' }]"
      />
    </div>
    
    <!-- 操作按钮 -->
    <div class="section">
      <van-button 
        type="primary" 
        block 
        :loading="isVerifying"
        @click="testSendCodeVerification"
      >
        测试发送验证码验证
      </van-button>
      
      <van-button 
        type="success" 
        block 
        :loading="isVerifying"
        @click="testSubmitVerification"
        style="margin-top: 12px;"
      >
        测试提交验证码验证
      </van-button>
    </div>
    
    <!-- 验证结果显示 -->
    <div class="section" v-if="lastResult">
      <h3>最后验证结果</h3>
      <div class="result-box">
        <p><strong>成功:</strong> {{ lastResult.success ? '是' : '否' }}</p>
        <p v-if="lastResult.error"><strong>错误:</strong> {{ lastResult.error }}</p>
        <p v-if="lastResult.geetestResult"><strong>Geetest结果:</strong> 有数据</p>
        <p v-if="lastResult.cloudflareResult"><strong>CloudFlare结果:</strong> {{ lastResult.cloudflareResult.token ? '有token' : '无token' }}</p>
      </div>
    </div>
    
    <!-- 构建的API参数显示 -->
    <div class="section" v-if="lastApiParams">
      <h3>构建的API参数</h3>
      <div class="result-box">
        <pre>{{ JSON.stringify(lastApiParams, null, 2) }}</pre>
      </div>
    </div>
    
    <!-- 统一验证对话框 -->
    <UnifiedVerificationDialog
      v-model:show-dialog="unifiedVerification.showVerifyDialog.value"
      :verify-url="unifiedVerification.verifyUrl.value"
      :verification-status="unifiedVerification.verificationStatus.value"
      :can-interrupt="unifiedVerification.canInterrupt.value"
      title="测试验证"
      @cancel="unifiedVerification.closeVerify"
      @retry="handleRetry"
      @iframe-load="handleIframeLoad"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { showToast } from "vant";
import { 
  useUnifiedVerification, 
  UnifiedVerificationDialog,
  VerificationMode, 
  VERIFICATION_SCENES,
  type UnifiedVerifyResult
} from "./index";

// 响应式数据
const selectedMode = ref(VerificationMode.BOTH);
const selectedScene = ref("BIND_PHONE");
const phoneNumber = ref("**********");
const lastResult = ref<UnifiedVerifyResult | null>(null);
const lastApiParams = ref<any>(null);

// 计算验证配置
const verificationConfig = computed(() => {
  const sceneMap = {
    BIND_PHONE: VERIFICATION_SCENES.BIND_PHONE,
    CHANGE_PHONE: VERIFICATION_SCENES.CHANGE_PHONE,
    BIND_WITHDRAW_ACCOUNT: VERIFICATION_SCENES.BIND_WITHDRAW_ACCOUNT,
    FORGET_PASSWORD: VERIFICATION_SCENES.FORGET_PASSWORD,
  };
  
  return {
    mode: selectedMode.value,
    scene: sceneMap[selectedScene.value as keyof typeof sceneMap],
    enableLogging: true,
  };
});

// 初始化统一验证（需要在配置变化时重新初始化）
let unifiedVerification = useUnifiedVerification(verificationConfig.value);

// 监听配置变化，重新初始化验证
watch(verificationConfig, (newConfig) => {
  unifiedVerification = useUnifiedVerification(newConfig);
}, { deep: true });

// 计算是否正在验证
const isVerifying = computed(() => unifiedVerification.isVerifying.value);

// 测试发送验证码验证
const testSendCodeVerification = async () => {
  try {
    showToast("开始发送验证码验证...");
    
    const result = await unifiedVerification.verifyForSendCode(phoneNumber.value);
    lastResult.value = result;
    
    if (result.success) {
      // 构建API参数
      const baseParams = {
        phone: phoneNumber.value,
        telephoneCode: "+63",
        type: 1, // 示例短信类型
      };
      
      lastApiParams.value = unifiedVerification.buildApiParams(result, baseParams);
      
      showToast("发送验证码验证成功！");
      console.log("验证成功，API参数:", lastApiParams.value);
    } else {
      showToast(result.error || "发送验证码验证失败");
      console.error("验证失败:", result.error);
    }
  } catch (error) {
    console.error("验证异常:", error);
    showToast("验证异常，请检查控制台");
  }
};

// 测试提交验证码验证
const testSubmitVerification = async () => {
  try {
    showToast("开始提交验证码验证...");
    
    const result = await unifiedVerification.verifyForSubmit();
    lastResult.value = result;
    
    if (result.success) {
      // 构建API参数
      const baseParams = {
        phone: phoneNumber.value,
        verifyCode: "123456", // 示例验证码
        telephoneCode: "+63",
      };
      
      lastApiParams.value = unifiedVerification.buildApiParams(result, baseParams);
      
      showToast("提交验证码验证成功！");
      console.log("验证成功，API参数:", lastApiParams.value);
    } else {
      showToast(result.error || "提交验证码验证失败");
      console.error("验证失败:", result.error);
    }
  } catch (error) {
    console.error("验证异常:", error);
    showToast("验证异常，请检查控制台");
  }
};

// 处理验证重试
const handleRetry = () => {
  showToast("重试验证");
  // 可以根据需要重新触发验证
};

// 处理iframe加载
const handleIframeLoad = () => {
  console.log("CloudFlare验证iframe已加载");
};
</script>

<style scoped lang="scss">
.test-page {
  padding: 16px;
  max-width: 600px;
  margin: 0 auto;
  
  h2 {
    text-align: center;
    margin-bottom: 24px;
    color: #333;
  }
  
  .section {
    margin-bottom: 24px;
    
    h3 {
      margin-bottom: 12px;
      color: #666;
      font-size: 16px;
    }
    
    .van-radio {
      margin-bottom: 8px;
    }
  }
  
  .result-box {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #ddd;
    
    p {
      margin: 4px 0;
      font-size: 14px;
    }
    
    pre {
      font-size: 12px;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}
</style>

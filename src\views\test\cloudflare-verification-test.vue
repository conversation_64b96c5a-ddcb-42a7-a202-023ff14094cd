<template>
  <div class="cloudflare-test-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>🔐 CloudFlare 验证系统测试</h1>
      <p>完整的 CloudFlare Turnstile 验证功能测试平台</p>
      <button @click="goBack" class="back-btn">← 返回测试首页</button>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-container">
      <!-- CloudFlare 直接验证测试 -->
      <div class="test-section">
        <h2>🎯 CloudFlare 直接验证测试</h2>
        <div class="test-grid">
          <div class="test-card" v-for="scene in cloudflareScenes" :key="scene.key">
            <div class="card-header">
              <h3>{{ scene.title }}</h3>
              <span class="scene-badge">{{ scene.key }}</span>
            </div>
            <p class="card-description">{{ scene.description }}</p>
            <button
              @click="testCloudflareScene(scene.key, scene.title)"
              :disabled="isLoading"
              class="test-btn cloudflare-btn"
            >
              {{ isLoading ? "验证中..." : "开始验证" }}
            </button>
          </div>
        </div>
      </div>

      <!-- 手机验证流程测试 -->
      <div class="test-section">
        <h2>📱 手机验证流程测试</h2>
        <div class="test-grid">
          <div class="test-card" v-for="verifyType in phoneVerifyTypes" :key="verifyType.key">
            <div class="card-header">
              <h3>{{ verifyType.title }}</h3>
              <span class="type-badge">{{ verifyType.key }}</span>
            </div>
            <p class="card-description">{{ verifyType.description }}</p>
            <div class="card-actions">
              <input
                v-model="verifyType.initialPhone"
                type="tel"
                placeholder="初始手机号（可选）"
                class="phone-input"
              />
              <button
                @click="
                  testPhoneVerification(verifyType.key, verifyType.title, verifyType.initialPhone)
                "
                :disabled="isLoading"
                class="test-btn phone-btn"
              >
                {{ isLoading ? "测试中..." : "开始测试" }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试结果显示 -->
      <div class="test-section" v-if="testResults.length > 0">
        <h2>📊 测试结果</h2>
        <div class="results-container">
          <div class="results-header">
            <h3>测试历史 ({{ testResults.length }})</h3>
            <button @click="clearResults" class="clear-btn">清空结果</button>
          </div>
          <div class="results-list">
            <div
              v-for="(result, index) in testResults"
              :key="index"
              class="result-item"
              :class="{ success: result.success, failed: !result.success }"
            >
              <div class="result-header">
                <div class="result-info">
                  <h4>{{ result.title }}</h4>
                  <span class="result-type">{{ result.type }}</span>
                </div>
                <div class="result-status">
                  <span :class="['status-badge', result.success ? 'success' : 'failed']">
                    {{ result.success ? "✅ 成功" : "❌ 失败" }}
                  </span>
                  <span class="result-time">{{ formatTime(result.timestamp) }}</span>
                </div>
              </div>
              <div class="result-details">
                <div class="detail-row" v-if="result.scene">
                  <label>验证场景:</label>
                  <span>{{ result.scene }}</span>
                </div>
                <div class="detail-row" v-if="result.code !== undefined">
                  <label>返回代码:</label>
                  <span>{{ result.code }}</span>
                </div>
                <div class="detail-row" v-if="result.token !== undefined">
                  <label>Token 状态:</label>
                  <span>{{ result.token ? "已获取" : "未获取" }}</span>
                </div>
                <div class="detail-row" v-if="result.phone">
                  <label>手机号:</label>
                  <span>{{ result.phone }}</span>
                </div>
                <div class="detail-row" v-if="result.error">
                  <label>错误信息:</label>
                  <span class="error-text">{{ result.error }}</span>
                </div>
                <div class="detail-row" v-if="result.data">
                  <label>返回数据:</label>
                  <pre class="data-preview">{{ JSON.stringify(result.data, null, 2) }}</pre>
                </div>
              </div>
              <div class="result-actions">
                <button @click="copyResult(result)" class="copy-btn">复制结果</button>
                <button @click="removeResult(index)" class="remove-btn">删除</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="test-section">
        <h2>📖 使用说明</h2>
        <div class="info-card">
          <div class="info-section">
            <h3>CloudFlare 直接验证</h3>
            <ul>
              <li>直接测试 CloudFlare Turnstile 验证功能</li>
              <li>支持多种验证场景（登录、获取验证码、提现等）</li>
              <li>返回验证 Token 和状态码</li>
              <li>可以测试不同的验证模式（managed/invisible）</li>
            </ul>
          </div>

          <div class="info-section">
            <h3>手机验证流程</h3>
            <ul>
              <li>完整的手机号验证流程测试</li>
              <li>包含手机号输入、验证码发送、验证码验证</li>
              <li>集成 CloudFlare 验证（如果启用）</li>
              <li>支持多种验证类型（绑定、修改、忘记密码等）</li>
            </ul>
          </div>

          <div class="info-section">
            <h3>测试注意事项</h3>
            <ul>
              <li>确保网络连接正常，能够访问 CloudFlare 服务</li>
              <li>验证窗口可能需要几秒钟加载</li>
              <li>手机号验证为模拟实现，实际项目需要对接真实 API</li>
              <li>测试结果会保存在本地，刷新页面后清空</li>
            </ul>
          </div>

          <div class="info-section">
            <h3>错误排查</h3>
            <ul>
              <li>验证窗口不显示：检查网络和配置</li>
              <li>验证失败：确认 CloudFlare 配置正确</li>
              <li>Token 获取失败：检查验证是否真正完成</li>
              <li>查看浏览器控制台获取详细错误信息</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- CloudFlare 验证组件 -->
    <CloudflareVerification
      ref="cloudflareVerificationRef"
      :config="cloudflareConfig"
      @success="onCloudflareSuccess"
      @cancel="onVerificationCancel"
      @error="onVerificationError"
    />

    <!-- 手机验证组件 -->
    <PhoneVerification
      ref="phoneVerificationRef"
      :verify-type="currentVerifyType"
      :initial-phone="currentInitialPhone"
      @success="onVerificationSuccess"
      @cancel="onVerificationCancel"
      @error="onVerificationError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  CloudflareVerification,
  PhoneVerification,
  useCloudflareVerification,
} from "@/components/CloudFlare";
import type {
  CloudFlareScene,
  VerifyType,
  PhoneVerificationResult,
  VerifyResult,
} from "@/components/CloudFlare/types";

const router = useRouter();

/**
 * 简单的通知函数
 * @param message 消息内容
 */
const showNotification = (message: string) => {
  try {
    import("vant")
      .then(({ showToast }) => {
        showToast(message);
      })
      .catch(() => {
        console.log("📢 通知:", message);
      });
  } catch {
    console.log("📢 通知:", message);
  }
};

// 组件引用
const cloudflareVerificationRef = ref<InstanceType<typeof CloudflareVerification>>();
const phoneVerificationRef = ref<InstanceType<typeof PhoneVerification>>();

// CloudFlare 验证实例
const cloudflareVerification = useCloudflareVerification();

// 响应式数据
const isLoading = ref(false);
const currentVerifyType = ref<VerifyType>(0);
const currentInitialPhone = ref("");

// CloudFlare 配置
const cloudflareConfig = reactive({
  ActivityPageHost: {
    PRE: "https://example.com/",
    DEV: "https://dev.example.com/",
    TEST: "https://test.example.com/",
  },
  CloudflareVerify_SITE_KEY: {
    PRE: "0x4AAAAAABr6liO_iAPr4Zx_",
    DEV: "0x4AAAAAABr6liO_iAPr4Zx_",
    TEST: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  debug_mode_main: "PRE",
});

// 测试结果
interface TestResult {
  title: string;
  type: "CloudFlare" | "Phone";
  success: boolean;
  timestamp: number;
  scene?: string;
  code?: number;
  token?: string;
  phone?: string;
  error?: string;
  data?: any;
}

const testResults = ref<TestResult[]>([]);

// CloudFlare 验证场景
const cloudflareScenes = ref([
  {
    key: "SCENE_LOGIN" as CloudFlareScene,
    title: "登录验证",
    description: "用户登录时的安全验证",
  },
  {
    key: "SCENE_GET_CODE" as CloudFlareScene,
    title: "获取验证码",
    description: "手机验证码获取前的验证",
  },
  {
    key: "SCENE_FORGET_PASSWORD" as CloudFlareScene,
    title: "忘记密码",
    description: "密码重置流程的验证",
  },
  {
    key: "SCENE_WITHDRAW" as CloudFlareScene,
    title: "提现验证",
    description: "资金提取时的安全验证",
  },
  {
    key: "SCENE_SUB_KYC_INFO" as CloudFlareScene,
    title: "KYC 验证",
    description: "身份认证信息提交验证",
  },
  {
    key: "SCENE_BIND_PT_PHONE" as CloudFlareScene,
    title: "绑定手机",
    description: "绑定新手机号的验证",
  },
]);

// 手机验证类型
const phoneVerifyTypes = ref([
  {
    key: 0 as VerifyType,
    title: "绑定手机号",
    description: "首次绑定手机号码",
    initialPhone: "",
  },
  {
    key: 2 as VerifyType,
    title: "修改手机号",
    description: "更换绑定的手机号码",
    initialPhone: "09123456789",
  },
  {
    key: 1 as VerifyType,
    title: "忘记密码",
    description: "通过手机号重置密码",
    initialPhone: "09123456789",
  },
  {
    key: 3 as VerifyType,
    title: "添加提现账户",
    description: "添加新的提现账户",
    initialPhone: "",
  },
  {
    key: 4 as VerifyType,
    title: "修改提现账户",
    description: "修改现有提现账户",
    initialPhone: "",
  },
]);

// 方法
const goBack = () => {
  router.push("/test");
};

const testCloudflareScene = async (scene: CloudFlareScene, title: string) => {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    const result = await cloudflareVerification.getVerifyToken(scene);

    const testResult: TestResult = {
      title,
      type: "CloudFlare",
      success: !!result.token,
      timestamp: Date.now(),
      scene,
      code: result.code,
      token: result.token,
    };

    testResults.value.unshift(testResult);

    if (result.token) {
      showNotification(`${title}验证成功！`);
    } else {
      showNotification(`${title}验证失败！`);
    }
  } catch (error) {
    console.error("CloudFlare 验证错误:", error);

    const testResult: TestResult = {
      title,
      type: "CloudFlare",
      success: false,
      timestamp: Date.now(),
      scene,
      error: (error as Error).message,
    };

    testResults.value.unshift(testResult);
    showNotification("验证过程中发生错误");
  } finally {
    isLoading.value = false;
  }
};

const testPhoneVerification = (verifyType: VerifyType, title: string, initialPhone: string) => {
  if (isLoading.value) return;

  currentVerifyType.value = verifyType;
  currentInitialPhone.value = initialPhone;

  phoneVerificationRef.value?.startVerification(verifyType, initialPhone);
};

const onVerificationSuccess = (result: PhoneVerificationResult) => {
  const testResult: TestResult = {
    title: `手机验证 - ${getVerifyTypeTitle(result.verifyType)}`,
    type: "Phone",
    success: result.success,
    timestamp: Date.now(),
    phone: result.data?.phone,
    data: result.data,
  };

  testResults.value.unshift(testResult);
  showNotification("手机验证成功！");
};

const onVerificationCancel = () => {
  showNotification("验证已取消");
};

const onVerificationError = (error: Error) => {
  const testResult: TestResult = {
    title: `手机验证 - ${getVerifyTypeTitle(currentVerifyType.value)}`,
    type: "Phone",
    success: false,
    timestamp: Date.now(),
    error: error.message,
  };

  testResults.value.unshift(testResult);
  showNotification("验证失败");
};

const getVerifyTypeTitle = (verifyType: VerifyType): string => {
  const type = phoneVerifyTypes.value.find((t) => t.key === verifyType);
  return type?.title || "未知类型";
};

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

const copyResult = async (result: TestResult) => {
  const text = JSON.stringify(result, null, 2);

  try {
    await navigator.clipboard.writeText(text);
    showNotification("结果已复制到剪贴板");
  } catch (error) {
    console.error("复制失败:", error);
    showNotification("复制失败");
  }
};

const removeResult = (index: number) => {
  testResults.value.splice(index, 1);
  showNotification("结果已删除");
};

const clearResults = () => {
  testResults.value = [];
  showNotification("所有结果已清空");
};

// 生命周期
onMounted(() => {
  console.log("CloudFlare 验证测试页面已加载");
});
</script>

<style lang="scss" scoped>
.cloudflare-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: "Inter", sans-serif;
}

.page-header {
  text-align: center;
  color: white;
  margin-bottom: 40px;

  h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 20px;
  }

  .back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}

.test-container {
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;

  h2 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.test-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  h3 {
    color: #333;
    font-size: 1.25rem;
    margin: 0;
    font-weight: 600;
  }
}

.scene-badge,
.type-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.card-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 16px;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.phone-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
}

.test-btn {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.cloudflare-btn {
    background: #007bff;
    color: white;

    &:hover:not(:disabled) {
      background: #0056b3;
    }
  }

  &.phone-btn {
    background: #28a745;
    color: white;

    &:hover:not(:disabled) {
      background: #1e7e34;
    }
  }
}

.results-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    color: #333;
    margin: 0;
  }

  .clear-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;

    &:hover {
      background: #c82333;
    }
  }
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;

  &.success {
    border-left: 4px solid #28a745;
  }

  &.failed {
    border-left: 4px solid #dc3545;
  }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-info h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.result-type {
  background: #f8f9fa;
  color: #6c757d;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.75rem;
  margin-left: 8px;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;

  &.success {
    background: #d4edda;
    color: #155724;
  }

  &.failed {
    background: #f8d7da;
    color: #721c24;
  }
}

.result-time {
  color: #6c757d;
  font-size: 0.8rem;
}

.result-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px solid #f8f9fa;

  &:last-child {
    border-bottom: none;
  }

  label {
    font-weight: 600;
    color: #333;
    min-width: 100px;
  }

  span {
    color: #666;
    font-family: monospace;
  }
}

.error-text {
  color: #dc3545 !important;
}

.data-preview {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  max-height: 100px;
  overflow-y: auto;
  margin: 0;
}

.result-actions {
  display: flex;
  gap: 8px;

  button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
  }

  .copy-btn {
    background: #007bff;
    color: white;

    &:hover {
      background: #0056b3;
    }
  }

  .remove-btn {
    background: #6c757d;
    color: white;

    &:hover {
      background: #545b62;
    }
  }
}

.info-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.info-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  h3 {
    color: #333;
    margin-bottom: 12px;
    font-size: 1.1rem;
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      line-height: 1.5;
      color: #555;
    }
  }
}

@media (max-width: 768px) {
  .test-grid {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .test-card,
  .results-container,
  .info-card {
    padding: 20px;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .result-status {
    align-self: flex-end;
  }
}
</style>
